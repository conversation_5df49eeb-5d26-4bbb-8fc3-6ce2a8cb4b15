package com.community.management.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码生成工具类
 */
public class PasswordGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 生成admin密码的加密字符串
        String adminPassword = "admin";
        String encodedAdminPassword = encoder.encode(adminPassword);
        
        System.out.println("原始密码: " + adminPassword);
        System.out.println("加密后的密码: " + encodedAdminPassword);
        
        // 验证密码是否正确
        boolean matches = encoder.matches(adminPassword, encodedAdminPassword);
        System.out.println("密码验证结果: " + matches);
        
        // 生成其他常用密码
        String[] passwords = {"admin", "123456", "password", "user"};
        
        System.out.println("\n=== 常用密码加密结果 ===");
        for (String password : passwords) {
            String encoded = encoder.encode(password);
            System.out.println("密码: " + password + " -> " + encoded);
        }
    }
}
