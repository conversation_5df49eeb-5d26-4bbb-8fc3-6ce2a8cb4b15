package com.community.management.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.community.management.common.Result;
import com.community.management.dto.PageDTO;
import com.community.management.dto.UserDTO;
import com.community.management.entity.User;
import com.community.management.service.UserService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 创建用户
     * @param userDTO 用户信息
     * @return 创建的用户
     */
    @PostMapping
    @PreAuthorize("hasAuthority('user:create')")
    public Result<User> createUser(@Valid @RequestBody UserDTO userDTO) {
        User user = userService.createUser(userDTO);
        return Result.success(user);
    }

    /**
     * 更新用户
     * @param id 用户ID
     * @param userDTO 用户信息
     * @return 更新后的用户
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<User> updateUser(@PathVariable Long id, @Valid @RequestBody UserDTO userDTO) {
        User user = userService.updateUser(id, userDTO);
        return Result.success(user);
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('user:delete')")
    public Result<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success();
    }

    /**
     * 获取用户信息
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('user:read')")
    public Result<User> getUser(@PathVariable Long id) {
        User user = userService.getUserById(id);
        return Result.success(user);
    }

    /**
     * 获取所有用户
     * @return 用户列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('user:read')")
    public Result<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return Result.success(users);
    }

    /**
     * 分页获取用户
     * @param pageDTO 分页参数
     * @return 分页用户列表
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('user:read')")
    public Result<Page<User>> pageUsers(@Valid PageDTO pageDTO) {
        Page<User> page = userService.pageUsers(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }

    /**
     * 获取可见用户
     * @return 可见用户列表
     */
    @GetMapping("/visible")
    @PreAuthorize("hasAuthority('user:read')")
    public Result<List<User>> getVisibleUsers() {
        List<User> users = userService.getVisibleUsers();
        return Result.success(users);
    }

    /**
     * 分页获取可见用户
     * @param pageDTO 分页参数
     * @return 分页可见用户列表
     */
    @GetMapping("/visible/page")
    @PreAuthorize("hasAuthority('user:read')")
    public Result<Page<User>> pageVisibleUsers(@Valid PageDTO pageDTO) {
        Page<User> page = userService.pageVisibleUsers(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }

    /**
     * 修改密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    @PutMapping("/{id}/password")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<Void> changePassword(@PathVariable Long id,
                                      @RequestParam String oldPassword,
                                      @RequestParam String newPassword) {
        userService.changePassword(id, oldPassword, newPassword);
        return Result.success();
    }

    /**
     * 重置密码
     * @param id 用户ID
     * @return 操作结果
     */
    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasAuthority('user:update')")
    public Result<String> resetPassword(@PathVariable Long id) {
        String password = userService.resetPassword(id);
        return Result.success("密码重置成功", password);
    }
}
