package com.community.management.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.community.management.common.Result;
import com.community.management.dto.CommunityInfoDTO;
import com.community.management.dto.PageDTO;
import com.community.management.entity.CommunityInfo;
import com.community.management.service.CommunityInfoService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区信息控制器
 */
@RestController
@RequestMapping("/community-infos")
public class CommunityInfoController {

    @Autowired
    private CommunityInfoService communityInfoService;

    /**
     * 创建社区信息
     * @param communityInfoDTO 社区信息
     * @return 创建的社区信息
     */
    @PostMapping
    @PreAuthorize("hasAuthority('community_info:create')")
    public Result<CommunityInfo> createCommunityInfo(@Valid @RequestBody CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = communityInfoService.createCommunityInfo(communityInfoDTO);
        return Result.success(communityInfo);
    }

    /**
     * 更新社区信息
     * @param id 社区信息ID
     * @param communityInfoDTO 社区信息
     * @return 更新后的社区信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('community_info:update')")
    public Result<CommunityInfo> updateCommunityInfo(@PathVariable Long id, @Valid @RequestBody CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = communityInfoService.updateCommunityInfo(id, communityInfoDTO);
        return Result.success(communityInfo);
    }

    /**
     * 删除社区信息
     * @param id 社区信息ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('community_info:delete')")
    public Result<Void> deleteCommunityInfo(@PathVariable Long id) {
        communityInfoService.deleteCommunityInfo(id);
        return Result.success();
    }

    /**
     * 获取社区信息
     * @param id 社区信息ID
     * @return 社区信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<CommunityInfo> getCommunityInfo(@PathVariable Long id) {
        CommunityInfo communityInfo = communityInfoService.getCommunityInfoById(id);
        return Result.success(communityInfo);
    }

    /**
     * 获取所有社区信息
     * @return 社区信息列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<List<CommunityInfo>> getAllCommunityInfos() {
        List<CommunityInfo> communityInfos = communityInfoService.getAllCommunityInfos();
        return Result.success(communityInfos);
    }

    /**
     * 分页获取社区信息
     * @param pageDTO 分页参数
     * @return 分页社区信息列表
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<Page<CommunityInfo>> pageCommunityInfos(@Valid PageDTO pageDTO) {
        Page<CommunityInfo> page = communityInfoService.pageCommunityInfos(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }

    /**
     * 根据社区ID获取社区信息列表
     * @param communityId 社区ID
     * @return 社区信息列表
     */
    @GetMapping("/community/{communityId}")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<List<CommunityInfo>> getCommunityInfosByCommunityId(@PathVariable Long communityId) {
        List<CommunityInfo> communityInfos = communityInfoService.getCommunityInfosByCommunityId(communityId);
        return Result.success(communityInfos);
    }

    /**
     * 根据部门ID获取社区信息列表
     * @param departmentId 部门ID
     * @return 社区信息列表
     */
    @GetMapping("/department/{departmentId}")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<List<CommunityInfo>> getCommunityInfosByDepartmentId(@PathVariable Long departmentId) {
        List<CommunityInfo> communityInfos = communityInfoService.getCommunityInfosByDepartmentId(departmentId);
        return Result.success(communityInfos);
    }

    /**
     * 获取可见社区信息
     * @return 可见社区信息列表
     */
    @GetMapping("/visible")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<List<CommunityInfo>> getVisibleCommunityInfos() {
        List<CommunityInfo> communityInfos = communityInfoService.getVisibleCommunityInfos();
        return Result.success(communityInfos);
    }

    /**
     * 分页获取可见社区信息
     * @param pageDTO 分页参数
     * @return 分页可见社区信息列表
     */
    @GetMapping("/visible/page")
    @PreAuthorize("hasAuthority('community_info:read')")
    public Result<Page<CommunityInfo>> pageVisibleCommunityInfos(@Valid PageDTO pageDTO) {
        Page<CommunityInfo> page = communityInfoService.pageVisibleCommunityInfos(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }

    /**
     * 刷新二维码
     * @param id 社区信息ID
     * @return 新的二维码URL
     */
    @PutMapping("/{id}/refresh-qrcode")
    @PreAuthorize("hasAuthority('community_info:update')")
    public Result<String> refreshQrCode(@PathVariable Long id) {
        String qrCodeUrl = communityInfoService.refreshQrCode(id);
        return Result.success(qrCodeUrl);
    }
}