package com.community.management.controller;

import com.community.management.common.Result;
import com.community.management.dto.RoleDTO;
import com.community.management.entity.Role;
import com.community.management.service.RoleService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 */
@RestController
@RequestMapping("/roles")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 创建角色
     * @param roleDTO 角色信息
     * @return 创建的角色
     */
    @PostMapping
    @PreAuthorize("hasAuthority('role:create')")
    public Result<Role> createRole(@Valid @RequestBody RoleDTO roleDTO) {
        Role role = roleService.createRole(roleDTO);
        return Result.success(role);
    }

    /**
     * 更新角色
     * @param id 角色ID
     * @param roleDTO 角色信息
     * @return 更新后的角色
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('role:update')")
    public Result<Role> updateRole(@PathVariable Long id, @Valid @RequestBody RoleDTO roleDTO) {
        Role role = roleService.updateRole(id, roleDTO);
        return Result.success(role);
    }

    /**
     * 删除角色
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('role:delete')")
    public Result<Void> deleteRole(@PathVariable Long id) {
        roleService.deleteRole(id);
        return Result.success();
    }

    /**
     * 获取角色信息
     * @param id 角色ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('role:read')")
    public Result<Role> getRole(@PathVariable Long id) {
        Role role = roleService.getRoleById(id);
        return Result.success(role);
    }

    /**
     * 获取所有角色
     * @return 角色列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('role:read')")
    public Result<List<Role>> getAllRoles() {
        List<Role> roles = roleService.getAllRoles();
        return Result.success(roles);
    }

    /**
     * 根据用户ID获取角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('role:read')")
    public Result<List<Role>> getRolesByUserId(@PathVariable Long userId) {
        List<Role> roles = roleService.getRolesByUserId(userId);
        return Result.success(roles);
    }

    /**
     * 根据角色名获取角色
     * @param name 角色名
     * @return 角色信息
     */
    @GetMapping("/name/{name}")
    @PreAuthorize("hasAuthority('role:read')")
    public Result<Role> getRoleByName(@PathVariable String name) {
        Role role = roleService.getRoleByName(name);
        return Result.success(role);
    }
}
