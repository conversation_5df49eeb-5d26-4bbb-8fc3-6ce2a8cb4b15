package com.community.management.controller;

import com.community.management.common.Result;
import com.community.management.dto.DepartmentDTO;
import com.community.management.entity.Department;
import com.community.management.service.DepartmentService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门控制器
 */
@RestController
@RequestMapping("/departments")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 创建部门
     * @param departmentDTO 部门信息
     * @return 创建的部门
     */
    @PostMapping
    @PreAuthorize("hasAuthority('department:create')")
    public Result<Department> createDepartment(@Valid @RequestBody DepartmentDTO departmentDTO) {
        Department department = departmentService.createDepartment(departmentDTO);
        return Result.success(department);
    }

    /**
     * 更新部门
     * @param id 部门ID
     * @param departmentDTO 部门信息
     * @return 更新后的部门
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('department:update')")
    public Result<Department> updateDepartment(@PathVariable Long id, @Valid @RequestBody DepartmentDTO departmentDTO) {
        Department department = departmentService.updateDepartment(id, departmentDTO);
        return Result.success(department);
    }

    /**
     * 删除部门
     * @param id 部门ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('department:delete')")
    public Result<Void> deleteDepartment(@PathVariable Long id) {
        departmentService.deleteDepartment(id);
        return Result.success();
    }

    /**
     * 获取部门信息
     * @param id 部门ID
     * @return 部门信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('department:read')")
    public Result<Department> getDepartment(@PathVariable Long id) {
        Department department = departmentService.getDepartmentById(id);
        return Result.success(department);
    }

    /**
     * 获取部门树
     * @return 部门树
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('department:read')")
    public Result<List<Department>> getDepartmentTree() {
        List<Department> departments = departmentService.getDepartmentTree();
        return Result.success(departments);
    }

    /**
     * 获取可见部门树
     * @return 可见部门树
     */
    @GetMapping("/visible/tree")
    @PreAuthorize("hasAuthority('department:read')")
    public Result<List<Department>> getVisibleDepartmentTree() {
        List<Department> departments = departmentService.getVisibleDepartmentTree();
        return Result.success(departments);
    }

    /**
     * 获取子部门ID列表
     * @param id 部门ID
     * @return 子部门ID列表
     */
    @GetMapping("/{id}/children/ids")
    @PreAuthorize("hasAuthority('department:read')")
    public Result<List<Long>> getChildDepartmentIds(@PathVariable Long id) {
        List<Long> ids = departmentService.getChildDepartmentIds(id);
        return Result.success(ids);
    }
}