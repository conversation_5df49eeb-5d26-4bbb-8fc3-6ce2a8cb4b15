package com.community.management.controller;

import com.community.management.common.Result;
import com.community.management.dto.PermissionDTO;
import com.community.management.entity.Permission;
import com.community.management.service.PermissionService;
import javax.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限控制器
 */
@RestController
@RequestMapping("/api/permissions")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 创建权限
     * @param permissionDTO 权限信息
     * @return 创建的权限
     */
    @PostMapping
    @PreAuthorize("hasAuthority('permission:create')")
    public Result<Permission> createPermission(@Valid @RequestBody PermissionDTO permissionDTO) {
        Permission permission = permissionService.createPermission(permissionDTO);
        return Result.success(permission);
    }

    /**
     * 更新权限
     * @param id 权限ID
     * @param permissionDTO 权限信息
     * @return 更新后的权限
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('permission:update')")
    public Result<Permission> updatePermission(@PathVariable Long id, @Valid @RequestBody PermissionDTO permissionDTO) {
        Permission permission = permissionService.updatePermission(id, permissionDTO);
        return Result.success(permission);
    }

    /**
     * 删除权限
     * @param id 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('permission:delete')")
    public Result<Void> deletePermission(@PathVariable Long id) {
        permissionService.deletePermission(id);
        return Result.success();
    }

    /**
     * 获取权限信息
     * @param id 权限ID
     * @return 权限信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('permission:read')")
    public Result<Permission> getPermission(@PathVariable Long id) {
        Permission permission = permissionService.getPermissionById(id);
        return Result.success(permission);
    }

    /**
     * 获取所有权限
     * @return 权限列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('permission:read')")
    public Result<List<Permission>> getAllPermissions() {
        List<Permission> permissions = permissionService.getAllPermissions();
        return Result.success(permissions);
    }

    /**
     * 根据角色ID获取权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    @GetMapping("/role/{roleId}")
    @PreAuthorize("hasAuthority('permission:read')")
    public Result<List<Permission>> getPermissionsByRoleId(@PathVariable Long roleId) {
        List<Permission> permissions = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID获取权限列表
     * @param userId 用户ID
     * @return 权限列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('permission:read')")
    public Result<List<Permission>> getPermissionsByUserId(@PathVariable Long userId) {
        List<Permission> permissions = permissionService.getPermissionsByUserId(userId);
        return Result.success(permissions);
    }
}