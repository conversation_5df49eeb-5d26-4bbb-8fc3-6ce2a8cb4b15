package com.community.management.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.community.management.common.Result;
import com.community.management.dto.CommunityDTO;
import com.community.management.dto.PageDTO;
import com.community.management.entity.Community;
import com.community.management.service.CommunityService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区控制器
 */
@RestController
@RequestMapping("/communities")
public class CommunityController {

    @Autowired
    private CommunityService communityService;

    /**
     * 创建社区
     * @param communityDTO 社区信息
     * @return 创建的社区
     */
    @PostMapping
    @PreAuthorize("hasAuthority('community:create')")
    public Result<Community> createCommunity(@Valid @RequestBody CommunityDTO communityDTO) {
        Community community = communityService.createCommunity(communityDTO);
        return Result.success(community);
    }

    /**
     * 更新社区
     * @param id 社区ID
     * @param communityDTO 社区信息
     * @return 更新后的社区
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('community:update')")
    public Result<Community> updateCommunity(@PathVariable Long id, @Valid @RequestBody CommunityDTO communityDTO) {
        Community community = communityService.updateCommunity(id, communityDTO);
        return Result.success(community);
    }

    /**
     * 删除社区
     * @param id 社区ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('community:delete')")
    public Result<Void> deleteCommunity(@PathVariable Long id) {
        communityService.deleteCommunity(id);
        return Result.success();
    }

    /**
     * 获取社区信息
     * @param id 社区ID
     * @return 社区信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('community:read')")
    public Result<Community> getCommunity(@PathVariable Long id) {
        Community community = communityService.getCommunityById(id);
        return Result.success(community);
    }

    /**
     * 获取所有社区
     * @return 社区列表
     */
    @GetMapping
    @PreAuthorize("hasAuthority('community:read')")
    public Result<List<Community>> getAllCommunities() {
        List<Community> communities = communityService.getAllCommunities();
        return Result.success(communities);
    }

    /**
     * 分页获取社区
     * @param pageDTO 分页参数
     * @return 分页社区列表
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('community:read')")
    public Result<Page<Community>> pageCommunities(@Valid PageDTO pageDTO) {
        Page<Community> page = communityService.pageCommunities(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }

    /**
     * 根据部门ID获取社区列表
     * @param departmentId 部门ID
     * @return 社区列表
     */
    @GetMapping("/department/{departmentId}")
    @PreAuthorize("hasAuthority('community:read')")
    public Result<List<Community>> getCommunitiesByDepartmentId(@PathVariable Long departmentId) {
        List<Community> communities = communityService.getCommunitiesByDepartmentId(departmentId);
        return Result.success(communities);
    }

    /**
     * 获取可见社区
     * @return 可见社区列表
     */
    @GetMapping("/visible")
    @PreAuthorize("hasAuthority('community:read')")
    public Result<List<Community>> getVisibleCommunities() {
        List<Community> communities = communityService.getVisibleCommunities();
        return Result.success(communities);
    }

    /**
     * 分页获取可见社区
     * @param pageDTO 分页参数
     * @return 分页可见社区列表
     */
    @GetMapping("/visible/page")
    @PreAuthorize("hasAuthority('community:read')")
    public Result<Page<Community>> pageVisibleCommunities(@Valid PageDTO pageDTO) {
        Page<Community> page = communityService.pageVisibleCommunities(pageDTO.getPage(), pageDTO.getSize());
        return Result.success(page);
    }
}