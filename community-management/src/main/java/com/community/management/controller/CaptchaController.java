package com.community.management.controller;

import com.community.management.common.Result;
import com.community.management.util.CaptchaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码控制器
 */
@RestController
@RequestMapping("")
public class CaptchaController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取验证码
     * @return 验证码信息
     */
    @GetMapping("/captcha")
    public Result<Map<String, Object>> getCaptcha() {
        try {
            // 生成验证码
            String captchaText = CaptchaUtil.generateCaptcha(4);
            
            // 生成UUID作为验证码标识
            String uuid = UUID.randomUUID().toString();
            
            // 将验证码存储到Redis，有效期5分钟
            String redisKey = "captcha:" + uuid;
            redisTemplate.opsForValue().set(redisKey, captchaText.toLowerCase(), 5, TimeUnit.MINUTES);
            
            // 生成验证码图片的Base64编码
            String captchaImageBase64 = CaptchaUtil.generateCaptchaImageBase64(captchaText, 120, 40);

            // 提取Base64数据部分（去掉data:image/png;base64,前缀）
            String base64Data = captchaImageBase64.substring(captchaImageBase64.indexOf(",") + 1);

            Map<String, Object> result = new HashMap<>();
            result.put("uuid", uuid);
            result.put("img", base64Data);
            
            return Result.success(result);
        } catch (IOException e) {
            return Result.failed("生成验证码失败");
        }
    }

    /**
     * 验证验证码
     * @param uuid 验证码UUID
     * @param captcha 用户输入的验证码
     * @return 验证结果
     */
    public boolean verifyCaptcha(String uuid, String captcha) {
        if (uuid == null || captcha == null) {
            return false;
        }
        
        String redisKey = "captcha:" + uuid;
        String storedCaptcha = (String) redisTemplate.opsForValue().get(redisKey);
        
        if (storedCaptcha == null) {
            return false;
        }
        
        // 验证后删除验证码
        redisTemplate.delete(redisKey);
        
        // 不区分大小写比较
        return storedCaptcha.equalsIgnoreCase(captcha);
    }
}
