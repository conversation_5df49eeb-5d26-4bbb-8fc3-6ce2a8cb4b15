package com.community.management.controller;

import com.community.management.common.Result;
import com.community.management.controller.CaptchaController;
import com.community.management.dto.AuthRequestDTO;
import com.community.management.dto.AuthResponseDTO;
import com.community.management.dto.UserDTO;
import com.community.management.entity.User;
import com.community.management.exception.BusinessException;
import com.community.management.service.UserService;
import com.community.management.service.impl.UserDetailsServiceImpl;
import com.community.management.util.JwtUtil;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private CaptchaController captchaController;

    /**
     * 登录
     * @param authRequest 认证请求
     * @return 认证响应
     */
    @PostMapping("/login")
    public Result<AuthResponseDTO> login(@Valid @RequestBody AuthRequestDTO authRequest) {
        try {
            // 验证验证码
            if (authRequest.getCaptcha() != null && authRequest.getUuid() != null) {
                if (!captchaController.verifyCaptcha(authRequest.getUuid(), authRequest.getCaptcha())) {
                    throw new BusinessException("验证码错误", 400);
                }
            }

            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(authRequest.getUsername(), authRequest.getPassword()));

            final UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            final String token = jwtUtil.generateToken(userDetails);

            User user = (User) userDetails;
            return Result.success(new AuthResponseDTO(token, user));
        } catch (BadCredentialsException e) {
            e.printStackTrace();
            throw new BusinessException("用户名或密码错误", 401);
        }
    }

    /**
     * 注册
     * @param userDTO 用户信息
     * @return 认证响应
     */
    @PostMapping("/register")
    public Result<AuthResponseDTO> register(@Valid @RequestBody UserDTO userDTO) {
        // 创建用户
        User user = userService.createUser(userDTO);

        // 生成令牌
        final String token = jwtUtil.generateToken(user);

        return Result.success(new AuthResponseDTO(token, user));
    }
}
