package com.community.management.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.community.management.entity.User;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRepository extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户对象
     */
    @Select("SELECT * FROM user WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    /**
     * 查询部门及其子部门下的所有用户
     * @param departmentIds 部门ID列表
     * @return 用户列表
     */
    @Select("<script>" +
            "SELECT * FROM user WHERE department_id IN " +
            "<foreach collection='departmentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<User> findByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);
}