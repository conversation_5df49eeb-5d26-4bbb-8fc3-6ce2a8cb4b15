package com.community.management.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.community.management.entity.Permission;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PermissionRepository extends BaseMapper<Permission> {

    /**
     * 根据角色ID查询权限
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Select("SELECT p.* FROM permission p" +
            " JOIN role_permission rp ON p.id = rp.permission_id" +
            " WHERE rp.role_id = #{roleId}")
    List<Permission> findPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM permission p" +
            " JOIN role_permission rp ON p.id = rp.permission_id" +
            " JOIN user_role ur ON rp.role_id = ur.role_id" +
            " WHERE ur.user_id = #{userId}")
    List<Permission> findPermissionsByUserId(@Param("userId") Long userId);
}