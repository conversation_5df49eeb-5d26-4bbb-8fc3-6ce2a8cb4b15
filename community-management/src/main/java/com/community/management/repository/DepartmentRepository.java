package com.community.management.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.community.management.entity.Department;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DepartmentRepository extends BaseMapper<Department> {

    /**
     * 获取部门的所有子部门ID（包括自身）
     * @param departmentId 部门ID
     * @return 子部门ID列表
     */
    @Select("WITH RECURSIVE dept_tree AS (" +
            "  SELECT id FROM department WHERE id = #{departmentId}" +
            "  UNION ALL" +
            "  SELECT d.id FROM department d" +
            "  JOIN dept_tree dt ON d.parent_id = dt.id" +
            ")" +
            "SELECT id FROM dept_tree")
    List<Long> findChildDepartmentIds(@Param("departmentId") Long departmentId);

    /**
     * 根据父部门ID查询子部门
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Select("SELECT * FROM department WHERE parent_id = #{parentId}")
    List<Department> findByParentId(@Param("parentId") Long parentId);
}