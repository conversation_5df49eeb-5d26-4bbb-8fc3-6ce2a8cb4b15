package com.community.management.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.community.management.entity.Role;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleRepository extends BaseMapper<Role> {

    /**
     * 根据用户ID查询角色
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM role r" +
            " JOIN user_role ur ON r.id = ur.role_id" +
            " WHERE ur.user_id = #{userId}")
    List<Role> findRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据角色名查询角色
     * @param name 角色名
     * @return 角色对象
     */
    @Select("SELECT * FROM role WHERE name = #{name}")
    Role findRoleByName(@Param("name") String name);
}