package com.community.management.config;

import com.community.management.entity.Department;
import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.entity.User;
import com.community.management.repository.DepartmentRepository;
import com.community.management.repository.PermissionRepository;
import com.community.management.repository.RoleRepository;
import com.community.management.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据初始化类
 */
@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        // 检查是否已初始化
        if (userRepository.selectCount(null) > 0) {
            return;
        }

        // 初始化权限
        List<Permission> permissions = initPermissions();

        // 初始化角色
        List<Role> roles = initRoles(permissions);

        // 初始化部门
        List<Department> departments = initDepartments();

        // 初始化用户
        initUsers(roles, departments);
    }

    /**
     * 初始化权限
     * @return 权限列表
     */
    private List<Permission> initPermissions() {
        List<Permission> permissions = new ArrayList<>();

        // 用户权限
        permissions.add(createPermission("用户查询", "user:read", "用户查询权限"));
        permissions.add(createPermission("用户创建", "user:create", "用户创建权限"));
        permissions.add(createPermission("用户更新", "user:update", "用户更新权限"));
        permissions.add(createPermission("用户删除", "user:delete", "用户删除权限"));

        // 角色权限
        permissions.add(createPermission("角色查询", "role:read", "角色查询权限"));
        permissions.add(createPermission("角色创建", "role:create", "角色创建权限"));
        permissions.add(createPermission("角色更新", "role:update", "角色更新权限"));
        permissions.add(createPermission("角色删除", "role:delete", "角色删除权限"));

        // 权限权限
        permissions.add(createPermission("权限查询", "permission:read", "权限查询权限"));
        permissions.add(createPermission("权限创建", "permission:create", "权限创建权限"));
        permissions.add(createPermission("权限更新", "permission:update", "权限更新权限"));
        permissions.add(createPermission("权限删除", "permission:delete", "权限删除权限"));

        // 部门权限
        permissions.add(createPermission("部门查询", "department:read", "部门查询权限"));
        permissions.add(createPermission("部门创建", "department:create", "部门创建权限"));
        permissions.add(createPermission("部门更新", "department:update", "部门更新权限"));
        permissions.add(createPermission("部门删除", "department:delete", "部门删除权限"));

        // 社区权限
        permissions.add(createPermission("社区查询", "community:read", "社区查询权限"));
        permissions.add(createPermission("社区创建", "community:create", "社区创建权限"));
        permissions.add(createPermission("社区更新", "community:update", "社区更新权限"));
        permissions.add(createPermission("社区删除", "community:delete", "社区删除权限"));

        // 社区信息权限
        permissions.add(createPermission("社区信息查询", "community_info:read", "社区信息查询权限"));
        permissions.add(createPermission("社区信息创建", "community_info:create", "社区信息创建权限"));
        permissions.add(createPermission("社区信息更新", "community_info:update", "社区信息更新权限"));
        permissions.add(createPermission("社区信息删除", "community_info:delete", "社区信息删除权限"));

        permissions.forEach(permissionRepository::insert);
        return permissions;
    }

    /**
     * 创建权限
     * @param name 权限名称
     * @param permission 权限标识
     * @param description 权限描述
     * @return 权限
     */
    private Permission createPermission(String name, String permission, String description) {
        Permission p = new Permission();
        p.setName(name);
        p.setPermission(permission);
        p.setDescription(description);
        p.setCreateTime(LocalDateTime.now());
        p.setUpdateTime(LocalDateTime.now());
        p.setCreateBy("system");
        p.setUpdateBy("system");
        return p;
    }

    /**
     * 初始化角色
     * @param permissions 权限列表
     * @return 角色列表
     */
    private List<Role> initRoles(List<Permission> permissions) {
        List<Role> roles = new ArrayList<>();

        // 管理员角色
        Role adminRole = new Role();
        adminRole.setName("ROLE_ADMIN");
        adminRole.setDescription("管理员");
        adminRole.setPermissions(permissions);
        adminRole.setCreateTime(LocalDateTime.now());
        adminRole.setUpdateTime(LocalDateTime.now());
        adminRole.setCreateBy("system");
        adminRole.setUpdateBy("system");
        roles.add(adminRole);

        // 用户角色
        Role userRole = new Role();
        userRole.setName("ROLE_USER");
        userRole.setDescription("普通用户");
        userRole.setPermissions(permissions.stream()
                .filter(p -> p.getPermission().endsWith(":read"))
                .collect(Collectors.toList()));
        userRole.setCreateTime(LocalDateTime.now());
        userRole.setUpdateTime(LocalDateTime.now());
        userRole.setCreateBy("system");
        userRole.setUpdateBy("system");
        roles.add(userRole);

        roles.forEach(roleRepository::insert);
        return roles;
    }

    /**
     * 初始化部门
     * @return 部门列表
     */
    private List<Department> initDepartments() {
        List<Department> departments = new ArrayList<>();

        // 总部
        Department headquarters = new Department();
        headquarters.setName("总部");
        headquarters.setDescription("总部");
        headquarters.setSort(1);
        headquarters.setCreateTime(LocalDateTime.now());
        headquarters.setUpdateTime(LocalDateTime.now());
        headquarters.setCreateBy("system");
        headquarters.setUpdateBy("system");
        departments.add(headquarters);

        departments.forEach(departmentRepository::insert);
        return departments;
    }

    /**
     * 初始化用户
     * @param roles 角色列表
     * @param departments 部门列表
     */
    private void initUsers(List<Role> roles, List<Department> departments) {
        List<User> users = new ArrayList<>();

        // 管理员
        User admin = new User();
        admin.setUsername("admin");
        admin.setPassword(passwordEncoder.encode("admin"));
        admin.setNickname("管理员");
        admin.setEmail("<EMAIL>");
        admin.setPhone("13800000000");
        admin.setEnabled(true);
        admin.setRoles(roles.stream()
                .filter(r -> r.getName().equals("ROLE_ADMIN"))
                .collect(Collectors.toList()));
        admin.setDepartment(departments.get(0));
        admin.setCreateTime(LocalDateTime.now());
        admin.setUpdateTime(LocalDateTime.now());
        admin.setCreateBy("system");
        admin.setUpdateBy("system");
        users.add(admin);

        // 普通用户
        User user = new User();
        user.setUsername("user");
        user.setPassword(passwordEncoder.encode("user"));
        user.setNickname("普通用户");
        user.setEmail("<EMAIL>");
        user.setPhone("13900000000");
        user.setEnabled(true);
        user.setRoles(roles.stream()
                .filter(r -> r.getName().equals("ROLE_USER"))
                .collect(Collectors.toList()));
        user.setDepartment(departments.get(0));
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setCreateBy("system");
        user.setUpdateBy("system");
        users.add(user);

        users.forEach(userRepository::insert);
    }
}