package com.community.management.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${file.upload-dir:./uploads/}")
    private String uploadDir;

    @Value("${file.qrcode-dir:./qrcodes/}")
    private String qrcodeDir;

    /**
     * 配置静态资源访问
     * @param registry 资源处理器注册表
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 上传文件访问路径
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + new File(uploadDir).getAbsolutePath() + File.separator);

        // 二维码访问路径
        registry.addResourceHandler("/qrcodes/**")
                .addResourceLocations("file:" + new File(qrcodeDir).getAbsolutePath() + File.separator);
    }
}