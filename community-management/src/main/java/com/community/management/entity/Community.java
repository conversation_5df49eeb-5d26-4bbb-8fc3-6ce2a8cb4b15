package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "community")
@TableName("community")
public class Community {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String name;

    private String address;

    private String description;

    private String contactPerson;

    private String contactPhone;

    @Column(name = "department_id")
    private Long departmentId;

    // 移除外键关联，只保留ID字段
    @Transient
    private Department department;

    // 移除外键关联，只保留ID字段
    @Transient
    private List<CommunityInfo> communityInfos = new ArrayList<>();

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}