package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@TableName("community")
public class Community {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String address;

    private String description;

    private String contactPerson;

    private String contactPhone;

    @TableField("department_id")
    private Long departmentId;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private Department department;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private List<CommunityInfo> communityInfos = new ArrayList<>();

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}