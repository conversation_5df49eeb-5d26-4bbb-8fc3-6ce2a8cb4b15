package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("community_info")
public class CommunityInfo {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String title;

    private String content;

    private String type;

    private String imageUrl;

    private Boolean visible = true;

    private String qrcode;

    @TableField("qrcode_url")
    private String qrcodeUrl;

    @TableField("community_id")
    private Long communityId;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private Community community;

    @TableField("department_id")
    private Long departmentId;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private Department department;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}