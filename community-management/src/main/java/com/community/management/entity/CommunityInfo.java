package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "community_info")
@TableName("community_info")
public class CommunityInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    private String imageUrl;

    @Column(name = "qrcode_url")
    private String qrcodeUrl;

    @Column(name = "community_id")
    private Long communityId;

    // 移除外键关联，只保留ID字段
    @Transient
    private Community community;

    @Column(name = "department_id")
    private Long departmentId;

    // 移除外键关联，只保留ID字段
    @Transient
    private Department department;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}