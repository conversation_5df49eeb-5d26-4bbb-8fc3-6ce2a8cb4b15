package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@TableName("department")
public class Department {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String description;

    @TableField("parent_id")
    private Long parentId;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private Department parent;

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private List<Department> children = new ArrayList<>();

    // 不存储在数据库中的字段，用于业务逻辑
    @TableField(exist = false)
    private List<User> users = new ArrayList<>();

    private Integer sort;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}