package com.community.management.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "department")
@TableName("department")
public class Department {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String name;

    private String description;

    @Column(name = "parent_id")
    private Long parentId;

    // 移除外键关联，只保留ID字段
    @Transient
    private Department parent;

    // 移除外键关联，只保留ID字段
    @Transient
    private List<Department> children = new ArrayList<>();

    // 移除外键关联，只保留ID字段
    @Transient
    private List<User> users = new ArrayList<>();

    private Integer sort;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}