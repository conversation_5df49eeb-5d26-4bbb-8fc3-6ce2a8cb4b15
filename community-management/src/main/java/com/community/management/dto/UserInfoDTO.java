package com.community.management.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户信息DTO - 适配前端需求
 */
@Data
public class UserInfoDTO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 显示名称（使用nickname）
     */
    private String name;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 部门ID
     */
    private Long departmentId;
    
    /**
     * 角色列表（角色名称数组）
     */
    private List<String> roles;
    
    /**
     * 权限列表（权限标识数组）
     */
    private List<String> permissions;
}
