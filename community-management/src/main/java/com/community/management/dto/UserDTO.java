package com.community.management.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class UserDTO {

    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;

    @Size(min = 6, max = 100, message = "密码长度必须在6-100之间")
    private String password;

    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    private String nickname;

    @Email(message = "邮箱格式不正确")
    private String email;

    private String phone;

    @NotNull(message = "部门ID不能为空")
    private Long departmentId;

    private List<Long> roleIds;

    private Boolean enabled = true;
}