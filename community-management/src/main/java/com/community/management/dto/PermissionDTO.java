package com.community.management.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 权限数据传输对象
 */
@Data
public class PermissionDTO {

    private Long id;

    @NotBlank(message = "权限名称不能为空")
    @Size(max = 50, message = "权限名称长度不能超过50个字符")
    private String name;

    @NotBlank(message = "权限标识不能为空")
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String permission;

    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String description;

    @Size(max = 200, message = "权限路径长度不能超过200个字符")
    private String path;
}
