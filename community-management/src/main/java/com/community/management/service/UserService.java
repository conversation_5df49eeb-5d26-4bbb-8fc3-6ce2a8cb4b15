package com.community.management.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.community.management.dto.UserDTO;
import com.community.management.entity.User;

import java.util.List;

public interface UserService extends IService<User> {

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户对象
     */
    User findByUsername(String username);

    /**
     * 创建用户
     * @param userDTO 用户DTO
     * @return 创建的用户
     */
    User createUser(UserDTO userDTO);

    /**
     * 更新用户
     * @param id 用户ID
     * @param userDTO 用户DTO
     * @return 更新后的用户
     */
    User updateUser(Long id, UserDTO userDTO);

    /**
     * 删除用户
     * @param id 用户ID
     */
    void deleteUser(Long id);

    /**
     * 分页查询用户
     * @param page 分页参数
     * @param username 用户名（可选）
     * @param realName 真实姓名（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页用户列表
     */
    Page<User> findUsers(Page<User> page, String username, String realName, Long departmentId);

    /**
     * 获取当前用户可见的用户列表（同级和下级部门）
     * @param page 分页参数
     * @param username 用户名（可选）
     * @param realName 真实姓名（可选）
     * @return 分页用户列表
     */
    Page<User> findVisibleUsers(Page<User> page, String username, String realName);

    /**
     * 修改用户密码
     * @param id 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changePassword(Long id, String oldPassword, String newPassword);

    /**
     * 重置用户密码
     * @param id 用户ID
     * @return 新密码
     */
    String resetPassword(Long id);

    /**
     * 根据ID获取用户
     * @param id 用户ID
     * @return 用户
     */
    User getUserById(Long id);

    /**
     * 获取所有用户
     * @return 用户列表
     */
    List<User> getAllUsers();

    /**
     * 分页获取用户
     * @param page 页码
     * @param size 每页大小
     * @return 分页用户列表
     */
    Page<User> pageUsers(int page, int size);

    /**
     * 获取可见用户
     * @return 可见用户列表
     */
    List<User> getVisibleUsers();

    /**
     * 分页获取可见用户
     * @param page 页码
     * @param size 每页大小
     * @return 分页可见用户列表
     */
    Page<User> pageVisibleUsers(int page, int size);
}