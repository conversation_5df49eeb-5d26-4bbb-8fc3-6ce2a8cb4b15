package com.community.management.service.impl;

import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.entity.User;
import com.community.management.repository.PermissionRepository;
import com.community.management.repository.RoleRepository;
import com.community.management.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户详情服务实现类
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户名不存在");
        }

        // 加载用户的角色
        List<Role> roles = roleRepository.findRolesByUserId(user.getId());

        // 为每个角色加载权限
        for (Role role : roles) {
            List<Permission> permissions = permissionRepository.findPermissionsByRoleId(role.getId());
            role.setPermissions(permissions);
        }

        user.setRoles(roles);

        return user;
    }
}