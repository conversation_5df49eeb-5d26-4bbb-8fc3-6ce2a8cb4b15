package com.community.management.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.PermissionDTO;
import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.exception.BusinessException;
import com.community.management.repository.PermissionRepository;
import com.community.management.service.PermissionService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Service
@Transactional
public class PermissionServiceImpl extends ServiceImpl<PermissionRepository, Permission> implements PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    public Permission createPermission(PermissionDTO permissionDTO) {
        Permission permission = new Permission();
        BeanUtils.copyProperties(permissionDTO, permission);

        // 设置创建信息
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setCreateBy(SecurityUtil.getCurrentUsername());
        permission.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(permission);
        return permission;
    }

    @Override
    public Permission updatePermission(Long id, PermissionDTO permissionDTO) {
        Permission permission = getById(id);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }

        BeanUtils.copyProperties(permissionDTO, permission, "id", "createTime", "createBy");

        // 设置更新信息
        permission.setUpdateTime(LocalDateTime.now());
        permission.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(permission);
        return permission;
    }

    @Override
    public void deletePermission(Long id) {
        Permission permission = getById(id);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否有角色使用该权限
        if (permission.getRoles() != null && !permission.getRoles().isEmpty()) {
            List<String> roleNames = permission.getRoles().stream()
                    .map(Role::getName)
                    .collect(Collectors.toList());
            throw new BusinessException("该权限被以下角色使用，不能删除：" + String.join(", ", roleNames));
        }

        removeById(id);
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Long roleId) {
        return permissionRepository.findPermissionsByRoleId(roleId);
    }

    @Override
    public List<Permission> getPermissionsByUserId(Long userId) {
        return permissionRepository.findPermissionsByUserId(userId);
    }

    @Override
    public List<Permission> getAllPermissions() {
        return list();
    }

    @Override
    public Permission getPermissionById(Long id) {
        return getById(id);
    }
}
