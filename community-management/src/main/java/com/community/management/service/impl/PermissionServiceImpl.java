package com.community.management.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.exception.BusinessException;
import com.community.management.repository.PermissionRepository;
import com.community.management.service.PermissionService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PermissionServiceImpl extends ServiceImpl<PermissionRepository, Permission> implements PermissionService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    @Transactional
    public Permission createPermission(Permission permission) {
        // 设置创建信息
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setCreateBy(SecurityUtil.getCurrentUsername());
        permission.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(permission);
        return permission;
    }

    @Override
    @Transactional
    public Permission updatePermission(Long id, Permission permission) {
        Permission existingPermission = getById(id);
        if (existingPermission == null) {
            throw new BusinessException("权限不存在");
        }

        // 更新权限信息
        permission.setId(id);
        permission.setUpdateTime(LocalDateTime.now());
        permission.setUpdateBy(SecurityUtil.getCurrentUsername());
        permission.setCreateTime(existingPermission.getCreateTime());
        permission.setCreateBy(existingPermission.getCreateBy());

        updateById(permission);
        return permission;
    }

    @Override
    @Transactional
    public void deletePermission(Long id) {
        Permission permission = getById(id);
        if (permission == null) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否有角色使用该权限
        if (permission.getRoles() != null && !permission.getRoles().isEmpty()) {
            List<String> roleNames = permission.getRoles().stream()
                    .map(Role::getName)
                    .collect(Collectors.toList());
            throw new BusinessException("该权限被以下角色使用，不能删除：" + String.join(", ", roleNames));
        }

        removeById(id);
    }

    @Override
    public Permission getPermissionById(Long id) {
        return getById(id);
    }

    @Override
    public List<Permission> getAllPermissions() {
        return list();
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Long roleId) {
        return permissionRepository.findPermissionsByRoleId(roleId);
    }

    @Override
    public List<Permission> getPermissionsByUserId(Long userId) {
        return permissionRepository.findPermissionsByUserId(userId);
    }
}