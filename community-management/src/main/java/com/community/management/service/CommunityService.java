package com.community.management.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.community.management.dto.CommunityDTO;
import com.community.management.entity.Community;

import java.util.List;

public interface CommunityService extends IService<Community> {

    /**
     * 创建小区
     * @param communityDTO 小区DTO
     * @return 创建的小区
     */
    Community createCommunity(CommunityDTO communityDTO);

    /**
     * 更新小区
     * @param id 小区ID
     * @param communityDTO 小区DTO
     * @return 更新后的小区
     */
    Community updateCommunity(Long id, CommunityDTO communityDTO);

    /**
     * 删除小区
     * @param id 小区ID
     */
    void deleteCommunity(Long id);

    /**
     * 分页查询小区
     * @param page 分页参数
     * @param name 小区名称（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页小区列表
     */
    Page<Community> findCommunities(Page<Community> page, String name, Long departmentId);

    /**
     * 获取当前用户可见的小区列表（同级和下级部门）
     * @param page 分页参数
     * @param name 小区名称（可选）
     * @return 分页小区列表
     */
    Page<Community> findVisibleCommunities(Page<Community> page, String name);

    /**
     * 根据ID获取小区
     * @param id 小区ID
     * @return 小区
     */
    Community getCommunityById(Long id);

    /**
     * 获取所有社区
     * @return 社区列表
     */
    List<Community> getAllCommunities();

    /**
     * 分页获取社区
     * @param page 页码
     * @param size 每页大小
     * @return 分页社区列表
     */
    Page<Community> pageCommunities(int page, int size);

    /**
     * 根据部门ID获取社区列表
     * @param departmentId 部门ID
     * @return 社区列表
     */
    List<Community> getCommunitiesByDepartmentId(Long departmentId);

    /**
     * 获取可见社区
     * @return 可见社区列表
     */
    List<Community> getVisibleCommunities();

    /**
     * 分页获取可见社区
     * @param page 页码
     * @param size 每页大小
     * @return 分页可见社区列表
     */
    Page<Community> pageVisibleCommunities(int page, int size);
}