package com.community.management.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.community.management.dto.CommunityInfoDTO;
import com.community.management.entity.CommunityInfo;

import java.util.List;

public interface CommunityInfoService extends IService<CommunityInfo> {

    /**
     * 创建小区信息
     * @param communityInfoDTO 小区信息DTO
     * @return 创建的小区信息
     */
    CommunityInfo createCommunityInfo(CommunityInfoDTO communityInfoDTO);

    /**
     * 更新小区信息
     * @param id 小区信息ID
     * @param communityInfoDTO 小区信息DTO
     * @return 更新后的小区信息
     */
    CommunityInfo updateCommunityInfo(Long id, CommunityInfoDTO communityInfoDTO);

    /**
     * 删除小区信息
     * @param id 小区信息ID
     */
    void deleteCommunityInfo(Long id);

    /**
     * 分页查询小区信息
     * @param page 分页参数
     * @param title 标题（可选）
     * @param communityId 小区ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 分页小区信息列表
     */
    Page<CommunityInfo> findCommunityInfos(Page<CommunityInfo> page, String title, Long communityId, Long departmentId);

    /**
     * 获取当前用户可见的小区信息列表（同级和下级部门）
     * @param page 分页参数
     * @param title 标题（可选）
     * @param communityId 小区ID（可选）
     * @return 分页小区信息列表
     */
    Page<CommunityInfo> findVisibleCommunityInfos(Page<CommunityInfo> page, String title, Long communityId);

    /**
     * 根据ID获取小区信息
     * @param id 小区信息ID
     * @return 小区信息
     */
    CommunityInfo getCommunityInfoById(Long id);

    /**
     * 生成小区信息二维码
     * @param id 小区信息ID
     * @return 二维码URL
     */
    String generateQRCode(Long id);

    /**
     * 根据小区ID获取小区信息列表
     * @param communityId 小区ID
     * @return 小区信息列表
     */
    List<CommunityInfo> findByCommunityId(Long communityId);

    /**
     * 获取所有社区信息
     * @return 社区信息列表
     */
    List<CommunityInfo> getAllCommunityInfos();

    /**
     * 分页获取社区信息
     * @param page 页码
     * @param size 每页大小
     * @return 分页社区信息列表
     */
    Page<CommunityInfo> pageCommunityInfos(int page, int size);

    /**
     * 根据社区ID获取社区信息列表
     * @param communityId 社区ID
     * @return 社区信息列表
     */
    List<CommunityInfo> getCommunityInfosByCommunityId(Long communityId);

    /**
     * 根据部门ID获取社区信息列表
     * @param departmentId 部门ID
     * @return 社区信息列表
     */
    List<CommunityInfo> getCommunityInfosByDepartmentId(Long departmentId);

    /**
     * 获取可见社区信息
     * @return 可见社区信息列表
     */
    List<CommunityInfo> getVisibleCommunityInfos();

    /**
     * 分页获取可见社区信息
     * @param page 页码
     * @param size 每页大小
     * @return 分页可见社区信息列表
     */
    Page<CommunityInfo> pageVisibleCommunityInfos(int page, int size);

    /**
     * 生成二维码URL
     * @return 二维码URL
     */
    String generateQrCodeUrl();

    /**
     * 刷新二维码
     * @param id 社区信息ID
     * @return 新的二维码URL
     */
    String refreshQrCode(Long id);


}