package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.RoleDTO;
import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.exception.BusinessException;
import com.community.management.repository.RoleRepository;
import com.community.management.service.PermissionService;
import com.community.management.service.RoleService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色服务实现类
 */
@Service
@Transactional
public class RoleServiceImpl extends ServiceImpl<RoleRepository, Role> implements RoleService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionService permissionService;

    @Override
    public Role createRole(RoleDTO roleDTO) {
        // 检查角色名是否已存在
        if (roleRepository.findRoleByName(roleDTO.getName()) != null) {
            throw new BusinessException("角色名已存在");
        }

        Role role = new Role();
        BeanUtils.copyProperties(roleDTO, role);

        // 设置权限
        if (!CollectionUtils.isEmpty(roleDTO.getPermissionIds())) {
            List<Permission> permissions = permissionService.listByIds(roleDTO.getPermissionIds());
            role.setPermissions(permissions);
        }

        // 设置创建信息
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setCreateBy(SecurityUtil.getCurrentUsername());
        role.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(role);
        return role;
    }

    @Override
    public Role updateRole(Long id, RoleDTO roleDTO) {
        Role role = getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查角色名是否已被其他角色使用
        if (!role.getName().equals(roleDTO.getName())) {
            Role existingRole = roleRepository.findRoleByName(roleDTO.getName());
            if (existingRole != null) {
                throw new BusinessException("角色名已被其他角色使用");
            }
        }

        BeanUtils.copyProperties(roleDTO, role, "id", "createTime", "createBy");

        // 更新权限
        if (roleDTO.getPermissionIds() != null) {
            if (roleDTO.getPermissionIds().isEmpty()) {
                role.setPermissions(new ArrayList<>());
            } else {
                List<Permission> permissions = permissionService.listByIds(roleDTO.getPermissionIds());
                role.setPermissions(permissions);
            }
        }

        // 设置更新信息
        role.setUpdateTime(LocalDateTime.now());
        role.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(role);
        return role;
    }

    @Override
    public void deleteRole(Long id) {
        Role role = getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查是否有用户使用该角色
        if (role.getUsers() != null && !role.getUsers().isEmpty()) {
            throw new BusinessException("该角色被用户使用，不能删除");
        }

        removeById(id);
    }

    @Override
    public List<Role> getRolesByUserId(Long userId) {
        return roleRepository.findRolesByUserId(userId);
    }

    @Override
    public Role getRoleByName(String name) {
        return roleRepository.findRoleByName(name);
    }

    @Override
    public Page<Role> findRoles(Page<Role> page, String name) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Role::getName, name);
        }
        return page(page, queryWrapper);
    }

    @Override
    public List<Role> getAllRoles() {
        return list();
    }

    @Override
    public Role getRoleById(Long id) {
        return getById(id);
    }
}
