package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.RoleDTO;
import com.community.management.entity.Permission;
import com.community.management.entity.Role;
import com.community.management.exception.BusinessException;
import com.community.management.repository.PermissionRepository;
import com.community.management.repository.RoleRepository;
import com.community.management.service.RoleService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl extends ServiceImpl<RoleRepository, Role> implements RoleService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    @Transactional
    public Role createRole(RoleDTO roleDTO) {
        // 检查角色名是否已存在
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getName, roleDTO.getName());
        if (count(queryWrapper) > 0) {
            throw new BusinessException("角色名已存在");
        }

        Role role = new Role();
        BeanUtils.copyProperties(roleDTO, role);

        // 设置创建信息
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setCreateBy(SecurityUtil.getCurrentUsername());
        role.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(role);

        // 设置权限
        if (!CollectionUtils.isEmpty(roleDTO.getPermissionIds())) {
            List<Permission> permissions = permissionRepository.selectBatchIds(roleDTO.getPermissionIds());
            role.setPermissions(permissions);
        }

        return role;
    }

    @Override
    @Transactional
    public Role updateRole(Long id, RoleDTO roleDTO) {
        Role role = getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查角色名是否已存在
        if (!role.getName().equals(roleDTO.getName())) {
            LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Role::getName, roleDTO.getName());
            if (count(queryWrapper) > 0) {
                throw new BusinessException("角色名已存在");
            }
        }

        BeanUtils.copyProperties(roleDTO, role);

        // 设置更新信息
        role.setUpdateTime(LocalDateTime.now());
        role.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(role);

        // 更新权限
        if (roleDTO.getPermissionIds() != null) {
            // 清除原有权限
            role.setPermissions(new ArrayList<>());

            // 设置新权限
            if (!CollectionUtils.isEmpty(roleDTO.getPermissionIds())) {
                List<Permission> permissions = permissionRepository.selectBatchIds(roleDTO.getPermissionIds());
                role.setPermissions(permissions);
            }
        }

        return role;
    }

    @Override
    @Transactional
    public void deleteRole(Long id) {
        Role role = getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查是否有用户使用该角色
        if (role.getUsers() != null && !role.getUsers().isEmpty()) {
            throw new BusinessException("该角色下有用户，不能删除");
        }

        // 清除权限关联
        role.setPermissions(new ArrayList<>());
        updateById(role);

        // 删除角色
        removeById(id);
    }

    @Override
    public Role getRoleById(Long id) {
        return getById(id);
    }

    @Override
    public List<Role> getAllRoles() {
        return list();
    }

    @Override
    public List<Role> getRolesByUserId(Long userId) {
        return roleRepository.findRolesByUserId(userId);
    }

    @Override
    public Role getRoleByName(String name) {
        return roleRepository.findRoleByName(name);
    }

    @Override
    public Page<Role> findRoles(Page<Role> page, String name) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Role::getName, name);
        }
        return page(page, queryWrapper);
    }
}