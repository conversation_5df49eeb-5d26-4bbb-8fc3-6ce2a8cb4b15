package com.community.management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.community.management.dto.PermissionDTO;
import com.community.management.entity.Permission;

import java.util.List;

/**
 * 权限服务接口
 */
public interface PermissionService extends IService<Permission> {

    /**
     * 创建权限
     * @param permissionDTO 权限信息
     * @return 创建的权限
     */
    Permission createPermission(PermissionDTO permissionDTO);

    /**
     * 更新权限
     * @param id 权限ID
     * @param permissionDTO 权限信息
     * @return 更新后的权限
     */
    Permission updatePermission(Long id, PermissionDTO permissionDTO);

    /**
     * 删除权限
     * @param id 权限ID
     */
    void deletePermission(Long id);

    /**
     * 根据ID获取权限
     * @param id 权限ID
     * @return 权限信息
     */
    Permission getPermissionById(Long id);

    /**
     * 获取所有权限
     * @return 权限列表
     */
    List<Permission> getAllPermissions();

    /**
     * 根据角色ID获取权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID获取权限列表
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByUserId(Long userId);
}