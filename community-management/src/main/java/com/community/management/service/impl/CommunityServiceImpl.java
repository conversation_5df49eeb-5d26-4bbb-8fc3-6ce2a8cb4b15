package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.CommunityDTO;
import com.community.management.entity.Community;
import com.community.management.entity.Department;
import com.community.management.entity.User;
import com.community.management.exception.BusinessException;
import com.community.management.repository.CommunityRepository;
import com.community.management.service.CommunityService;
import com.community.management.service.DepartmentService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CommunityServiceImpl extends ServiceImpl<CommunityRepository, Community> implements CommunityService {

    @Autowired
    private CommunityRepository communityRepository;

    @Autowired
    private DepartmentService departmentService;

    @Override
    @Transactional
    public Community createCommunity(CommunityDTO communityDTO) {
        Community community = new Community();
        BeanUtils.copyProperties(communityDTO, community);

        // 检查部门是否存在
        if (communityDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            community.setDepartment(department);
        }

        // 设置创建信息
        community.setCreateTime(LocalDateTime.now());
        community.setUpdateTime(LocalDateTime.now());
        community.setCreateBy(SecurityUtil.getCurrentUsername());
        community.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(community);
        return community;
    }

    @Override
    @Transactional
    public Community updateCommunity(Long id, CommunityDTO communityDTO) {
        Community community = getById(id);
        if (community == null) {
            throw new BusinessException("社区不存在");
        }

        BeanUtils.copyProperties(communityDTO, community);

        // 检查部门是否存在
        if (communityDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            community.setDepartment(department);
        } else {
            community.setDepartment(null);
        }

        // 设置更新信息
        community.setUpdateTime(LocalDateTime.now());
        community.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(community);
        return community;
    }

    @Override
    @Transactional
    public void deleteCommunity(Long id) {
        Community community = getById(id);
        if (community == null) {
            throw new BusinessException("社区不存在");
        }

        // 检查是否有社区信息
        if (community.getCommunityInfos() != null && !community.getCommunityInfos().isEmpty()) {
            throw new BusinessException("该社区下有社区信息，不能删除");
        }

        removeById(id);
    }

    @Override
    public Community getCommunityById(Long id) {
        return getById(id);
    }

    @Override
    public List<Community> getAllCommunities() {
        return list();
    }

    @Override
    public Page<Community> pageCommunities(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public List<Community> getCommunitiesByDepartmentId(Long departmentId) {
        return communityRepository.findByDepartmentId(departmentId);
    }

    @Override
    public List<Community> getVisibleCommunities() {
        // 获取当前用户所在部门
        User currentUser = SecurityUtil.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("未登录");
        }

        Department department = currentUser.getDepartment();
        if (department == null) {
            throw new BusinessException("当前用户未关联部门");
        }

        // 获取当前部门及其子部门
        List<Long> departmentIds = departmentService.getChildDepartmentIds(department.getId());
        departmentIds.add(department.getId());

        // 获取这些部门下的社区
        return communityRepository.findByDepartmentIds(departmentIds);
    }

    @Override
    public Page<Community> pageVisibleCommunities(int page, int size) {
        // 获取当前用户所在部门
        User currentUser = SecurityUtil.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("未登录");
        }

        Department department = currentUser.getDepartment();
        if (department == null) {
            throw new BusinessException("当前用户未关联部门");
        }

        // 获取当前部门及其子部门
        List<Long> departmentIds = departmentService.getChildDepartmentIds(department.getId());
        departmentIds.add(department.getId());

        // 构建查询条件
        LambdaQueryWrapper<Community> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Community::getDepartmentId, departmentIds);

        return page(new Page<>(page, size), queryWrapper);
    }
}