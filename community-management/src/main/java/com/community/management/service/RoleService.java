package com.community.management.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.community.management.dto.RoleDTO;
import com.community.management.entity.Role;

import java.util.List;

public interface RoleService extends IService<Role> {

    /**
     * 创建角色
     * @param roleDTO 角色DTO
     * @return 创建的角色
     */
    Role createRole(RoleDTO roleDTO);

    /**
     * 更新角色
     * @param id 角色ID
     * @param roleDTO 角色DTO
     * @return 更新后的角色
     */
    Role updateRole(Long id, RoleDTO roleDTO);

    /**
     * 删除角色
     * @param id 角色ID
     */
    void deleteRole(Long id);

    /**
     * 分页查询角色
     * @param page 分页参数
     * @param name 角色名（可选）
     * @return 分页角色列表
     */
    Page<Role> findRoles(Page<Role> page, String name);

    /**
     * 根据ID获取角色
     * @param id 角色ID
     * @return 角色
     */
    Role getRoleById(Long id);

    /**
     * 获取所有角色
     * @return 角色列表
     */
    List<Role> getAllRoles();

    /**
     * 根据用户ID获取角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(Long userId);

    /**
     * 根据角色名获取角色
     * @param name 角色名
     * @return 角色
     */
    Role getRoleByName(String name);

}