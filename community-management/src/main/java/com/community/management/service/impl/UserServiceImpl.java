package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.UserDTO;
import com.community.management.entity.Department;
import com.community.management.entity.Role;
import com.community.management.entity.User;
import com.community.management.exception.BusinessException;
import com.community.management.repository.RoleRepository;
import com.community.management.repository.UserRepository;
import com.community.management.service.DepartmentService;
import com.community.management.service.UserService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl extends ServiceImpl<UserRepository, User> implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    @Transactional
    public User createUser(UserDTO userDTO) {
        // 检查用户名是否已存在
        User existingUser = findByUsername(userDTO.getUsername());
        if (existingUser != null) {
            throw new BusinessException("用户名已存在");
        }

        User user = new User();
        BeanUtils.copyProperties(userDTO, user);

        // 设置密码
        if (StringUtils.hasText(userDTO.getPassword())) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        } else {
            // 默认密码为123456
            user.setPassword(passwordEncoder.encode("123456"));
        }

        // 设置部门
        Department department = departmentService.getById(userDTO.getDepartmentId());
        if (department == null) {
            throw new BusinessException("部门不存在");
        }
        user.setDepartmentId(userDTO.getDepartmentId());
        user.setDepartment(department);

        // 设置角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            List<Role> roles = roleRepository.selectBatchIds(userDTO.getRoleIds());
            user.setRoles(roles);
        }

        // 设置创建信息
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setCreateBy(SecurityUtil.getCurrentUsername());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(user);
        return user;
    }

    @Override
    @Transactional
    public User updateUser(Long id, UserDTO userDTO) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户名是否已存在（排除自身）
        if (!user.getUsername().equals(userDTO.getUsername())) {
            User existingUser = findByUsername(userDTO.getUsername());
            if (existingUser != null) {
                throw new BusinessException("用户名已存在");
            }
        }

        BeanUtils.copyProperties(userDTO, user, "password");

        // 设置密码（如果提供了新密码）
        if (StringUtils.hasText(userDTO.getPassword())) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        // 设置部门
        Department department = departmentService.getById(userDTO.getDepartmentId());
        if (department == null) {
            throw new BusinessException("部门不存在");
        }
        user.setDepartmentId(userDTO.getDepartmentId());
        user.setDepartment(department);

        // 设置角色
        if (userDTO.getRoleIds() != null) {
            List<Role> roles = roleRepository.selectBatchIds(userDTO.getRoleIds());
            user.setRoles(roles);
        }

        // 设置更新信息
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(user);
        return user;
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 删除用户角色关联
        removeById(id);
    }

    @Override
    public Page<User> findUsers(Page<User> page, String username, String realName, Long departmentId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(username)) {
            queryWrapper.like(User::getUsername, username);
        }
        if (StringUtils.hasText(realName)) {
            queryWrapper.like(User::getRealName, realName);
        }
        if (departmentId != null) {
            queryWrapper.eq(User::getDepartmentId, departmentId);
        }
        return page(page, queryWrapper);
    }

    @Override
    public Page<User> findVisibleUsers(Page<User> page, String username, String realName) {
        // 获取当前用户所在部门
        User currentUser = SecurityUtil.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("未登录");
        }

        Department department = currentUser.getDepartment();
        if (department == null) {
            throw new BusinessException("当前用户未关联部门");
        }

        // 获取当前部门及其子部门的ID列表
        List<Long> departmentIds = departmentService.getChildDepartmentIds(department.getId());

        // 查询这些部门下的用户
        List<User> users = userRepository.findByDepartmentIds(departmentIds);

        // 手动分页和过滤
        if (StringUtils.hasText(username)) {
            users = users.stream()
                    .filter(user -> user.getUsername().contains(username))
                    .collect(Collectors.toList());
        }
        if (StringUtils.hasText(realName)) {
            users = users.stream()
                    .filter(user -> user.getRealName().contains(realName))
                    .collect(Collectors.toList());
        }

        // 计算总记录数
        long total = users.size();

        // 分页
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), users.size());
        if (start < users.size()) {
            users = users.subList(start, end);
        } else {
            users = new ArrayList<>();
        }

        // 设置分页结果
        page.setRecords(users);
        page.setTotal(total);

        return page;
    }

    @Override
    public boolean changePassword(Long id, String oldPassword, String newPassword) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        return updateById(user);
    }

    @Override
    public String resetPassword(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成随机密码
        String newPassword = generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(user);
        return newPassword;
    }

    /**
     * 生成随机密码
     * @return 随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
}