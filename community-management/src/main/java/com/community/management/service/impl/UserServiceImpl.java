package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.UserDTO;
import com.community.management.entity.Department;
import com.community.management.entity.Role;
import com.community.management.entity.User;
import com.community.management.exception.BusinessException;
import com.community.management.repository.UserRepository;
import com.community.management.service.DepartmentService;
import com.community.management.service.RoleService;
import com.community.management.service.UserService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<UserRepository, User> implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User createUser(UserDTO userDTO) {
        // 检查用户名是否已存在
        if (userRepository.findByUsername(userDTO.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }

        User user = new User();
        BeanUtils.copyProperties(userDTO, user);

        // 加密密码
        if (userDTO.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        // 设置部门
        if (userDTO.getDepartmentId() != null) {
            Department department = departmentService.getById(userDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            user.setDepartmentId(userDTO.getDepartmentId());
        }

        // 设置角色
        if (!CollectionUtils.isEmpty(userDTO.getRoleIds())) {
            List<Role> roles = roleService.listByIds(userDTO.getRoleIds());
            user.setRoles(roles);
        }

        // 设置创建信息
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setCreateBy(SecurityUtil.getCurrentUsername());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(user);
        return user;
    }

    @Override
    public User updateUser(Long id, UserDTO userDTO) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户名是否已被其他用户使用
        if (!user.getUsername().equals(userDTO.getUsername())) {
            User existingUser = userRepository.findByUsername(userDTO.getUsername());
            if (existingUser != null) {
                throw new BusinessException("用户名已被其他用户使用");
            }
        }

        BeanUtils.copyProperties(userDTO, user, "id", "createTime", "createBy");

        // 更新密码
        if (userDTO.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        }

        // 更新部门
        if (userDTO.getDepartmentId() != null) {
            Department department = departmentService.getById(userDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            user.setDepartmentId(userDTO.getDepartmentId());
        }

        // 更新角色
        if (userDTO.getRoleIds() != null) {
            if (userDTO.getRoleIds().isEmpty()) {
                user.setRoles(new ArrayList<>());
            } else {
                List<Role> roles = roleService.listByIds(userDTO.getRoleIds());
                user.setRoles(roles);
            }
        }

        // 设置更新信息
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(user);
        return user;
    }

    @Override
    public void deleteUser(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        removeById(id);
    }

    @Override
    public String resetPassword(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成随机密码
        String newPassword = generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(user);
        return newPassword;
    }

    @Override
    public User getUserById(Long id) {
        return getById(id);
    }

    @Override
    public List<User> getAllUsers() {
        return list();
    }

    @Override
    public Page<User> pageUsers(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public List<User> getVisibleUsers() {
        return list();
    }

    @Override
    public Page<User> pageVisibleUsers(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public Page<User> findVisibleUsers(Page<User> page, String username, String realName) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like(User::getUsername, username);
        }
        if (realName != null && !realName.trim().isEmpty()) {
            queryWrapper.like(User::getNickname, realName);
        }
        return page(page, queryWrapper);
    }

    @Override
    public Page<User> findUsers(Page<User> page, String username, String realName, Long departmentId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like(User::getUsername, username);
        }
        if (realName != null && !realName.trim().isEmpty()) {
            queryWrapper.like(User::getNickname, realName);
        }
        if (departmentId != null) {
            queryWrapper.eq(User::getDepartmentId, departmentId);
        }
        return page(page, queryWrapper);
    }

    @Override
    public boolean changePassword(Long id, String oldPassword, String newPassword) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateBy(SecurityUtil.getCurrentUsername());

        return updateById(user);
    }

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 生成随机密码
     * @return 随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 8; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
}
