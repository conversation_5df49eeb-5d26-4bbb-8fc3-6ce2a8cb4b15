package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.CommunityInfoDTO;
import com.community.management.entity.Community;
import com.community.management.entity.CommunityInfo;
import com.community.management.entity.Department;
import com.community.management.entity.User;
import com.community.management.exception.BusinessException;
import com.community.management.repository.CommunityInfoRepository;
import com.community.management.service.CommunityInfoService;
import com.community.management.service.CommunityService;
import com.community.management.service.DepartmentService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class CommunityInfoServiceImpl extends ServiceImpl<CommunityInfoRepository, CommunityInfo> implements CommunityInfoService {

    @Autowired
    private CommunityInfoRepository communityInfoRepository;

    @Autowired
    private CommunityService communityService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    @Transactional
    public CommunityInfo createCommunityInfo(CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = new CommunityInfo();
        BeanUtils.copyProperties(communityInfoDTO, communityInfo);

        // 检查社区是否存在
        if (communityInfoDTO.getCommunityId() != null) {
            Community community = communityService.getCommunityById(communityInfoDTO.getCommunityId());
            if (community == null) {
                throw new BusinessException("社区不存在");
            }
            communityInfo.setCommunityId(communityInfoDTO.getCommunityId());
            communityInfo.setCommunity(community);
        }

        // 检查部门是否存在
        if (communityInfoDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityInfoDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            communityInfo.setDepartmentId(communityInfoDTO.getDepartmentId());
            communityInfo.setDepartment(department);
        }

        // 生成二维码URL
        communityInfo.setQrcodeUrl(generateQrCodeUrl());

        // 设置创建信息
        communityInfo.setCreateTime(LocalDateTime.now());
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setCreateBy(SecurityUtil.getCurrentUsername());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(communityInfo);
        return communityInfo;
    }

    @Override
    @Transactional
    public CommunityInfo updateCommunityInfo(Long id, CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        BeanUtils.copyProperties(communityInfoDTO, communityInfo);

        // 检查社区是否存在
        if (communityInfoDTO.getCommunityId() != null) {
            Community community = communityService.getCommunityById(communityInfoDTO.getCommunityId());
            if (community == null) {
                throw new BusinessException("社区不存在");
            }
            communityInfo.setCommunityId(communityInfoDTO.getCommunityId());
            communityInfo.setCommunity(community);
        } else {
            communityInfo.setCommunityId(null);
            communityInfo.setCommunity(null);
        }

        // 检查部门是否存在
        if (communityInfoDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityInfoDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            communityInfo.setDepartmentId(communityInfoDTO.getDepartmentId());
            communityInfo.setDepartment(department);
        } else {
            communityInfo.setDepartmentId(null);
            communityInfo.setDepartment(null);
        }

        // 设置更新信息
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(communityInfo);
        return communityInfo;
    }

    @Override
    @Transactional
    public void deleteCommunityInfo(Long id) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        removeById(id);
    }

    @Override
    public CommunityInfo getCommunityInfoById(Long id) {
        return getById(id);
    }

    @Override
    public List<CommunityInfo> getAllCommunityInfos() {
        return list();
    }

    @Override
    public Page<CommunityInfo> pageCommunityInfos(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public List<CommunityInfo> getCommunityInfosByCommunityId(Long communityId) {
        LambdaQueryWrapper<CommunityInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommunityInfo::getCommunityId, communityId);
        return list(queryWrapper);
    }

    @Override
    public List<CommunityInfo> getCommunityInfosByDepartmentId(Long departmentId) {
        return communityInfoRepository.findByDepartmentId(departmentId);
    }

    @Override
    public List<CommunityInfo> getVisibleCommunityInfos() {
        // 获取当前用户所在部门
        User currentUser = SecurityUtil.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("未登录");
        }

        Department department = currentUser.getDepartment();
        if (department == null) {
            throw new BusinessException("当前用户未关联部门");
        }

        // 获取当前部门及其子部门
        List<Long> departmentIds = departmentService.getChildDepartmentIds(department.getId());
        departmentIds.add(department.getId());

        // 获取这些部门下的社区信息
        return communityInfoRepository.findByDepartmentIds(departmentIds);
    }

    @Override
    public Page<CommunityInfo> pageVisibleCommunityInfos(int page, int size) {
        // 获取当前用户所在部门
        User currentUser = SecurityUtil.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("未登录");
        }

        Department department = currentUser.getDepartment();
        if (department == null) {
            throw new BusinessException("当前用户未关联部门");
        }

        // 获取当前部门及其子部门
        List<Long> departmentIds = departmentService.getChildDepartmentIds(department.getId());
        departmentIds.add(department.getId());

        // 构建查询条件
        LambdaQueryWrapper<CommunityInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CommunityInfo::getDepartmentId, departmentIds);

        return page(new Page<>(page, size), queryWrapper);
    }

    @Override
    public String generateQrCodeUrl() {
        // 生成唯一标识
        String uuid = UUID.randomUUID().toString();
        // 实际项目中，这里应该调用二维码生成服务，生成二维码图片并上传到文件服务器
        // 这里简化处理，返回一个模拟的URL
        return "/api/qrcode/" + uuid;
    }

    @Override
    public List<CommunityInfo> findByCommunityId(Long communityId) {
        return getCommunityInfosByCommunityId(communityId);
    }

    @Override
    public String refreshQrCode(Long id) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        // 生成新的二维码URL
        String qrCodeUrl = generateQrCodeUrl();
        communityInfo.setQrcodeUrl(qrCodeUrl);

        // 设置更新信息
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(communityInfo);
        return qrCodeUrl;
    }

    @Override
    public String generateQRCode(Long id) {
        return refreshQrCode(id);
    }
}