package com.community.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.community.management.dto.CommunityInfoDTO;
import com.community.management.entity.Community;
import com.community.management.entity.CommunityInfo;
import com.community.management.entity.Department;
import com.community.management.exception.BusinessException;
import com.community.management.repository.CommunityInfoRepository;
import com.community.management.service.CommunityInfoService;
import com.community.management.service.CommunityService;
import com.community.management.service.DepartmentService;
import com.community.management.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 社区信息服务实现类
 */
@Service
@Transactional
public class CommunityInfoServiceImpl extends ServiceImpl<CommunityInfoRepository, CommunityInfo> implements CommunityInfoService {

    @Autowired
    private CommunityInfoRepository communityInfoRepository;

    @Autowired
    private CommunityService communityService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public CommunityInfo createCommunityInfo(CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = new CommunityInfo();
        BeanUtils.copyProperties(communityInfoDTO, communityInfo);

        // 检查社区是否存在
        if (communityInfoDTO.getCommunityId() != null) {
            Community community = communityService.getCommunityById(communityInfoDTO.getCommunityId());
            if (community == null) {
                throw new BusinessException("社区不存在");
            }
            communityInfo.setCommunityId(communityInfoDTO.getCommunityId());
        }

        // 检查部门是否存在
        if (communityInfoDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityInfoDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            communityInfo.setDepartmentId(communityInfoDTO.getDepartmentId());
        }

        // 生成二维码URL
        communityInfo.setQrcodeUrl(generateQrCodeUrl());

        // 设置创建信息
        communityInfo.setCreateTime(LocalDateTime.now());
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setCreateBy(SecurityUtil.getCurrentUsername());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        save(communityInfo);
        return communityInfo;
    }

    @Override
    public CommunityInfo updateCommunityInfo(Long id, CommunityInfoDTO communityInfoDTO) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        BeanUtils.copyProperties(communityInfoDTO, communityInfo, "id", "createTime", "createBy", "qrcodeUrl");

        // 检查社区是否存在
        if (communityInfoDTO.getCommunityId() != null) {
            Community community = communityService.getCommunityById(communityInfoDTO.getCommunityId());
            if (community == null) {
                throw new BusinessException("社区不存在");
            }
            communityInfo.setCommunityId(communityInfoDTO.getCommunityId());
        } else {
            communityInfo.setCommunityId(null);
        }

        // 检查部门是否存在
        if (communityInfoDTO.getDepartmentId() != null) {
            Department department = departmentService.getDepartmentById(communityInfoDTO.getDepartmentId());
            if (department == null) {
                throw new BusinessException("部门不存在");
            }
            communityInfo.setDepartmentId(communityInfoDTO.getDepartmentId());
        } else {
            communityInfo.setDepartmentId(null);
        }

        // 设置更新信息
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(communityInfo);
        return communityInfo;
    }

    @Override
    public void deleteCommunityInfo(Long id) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        removeById(id);
    }

    @Override
    public List<CommunityInfo> findByCommunityId(Long communityId) {
        return getCommunityInfosByCommunityId(communityId);
    }

    @Override
    public List<CommunityInfo> getAllCommunityInfos() {
        return list();
    }

    @Override
    public Page<CommunityInfo> pageCommunityInfos(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public List<CommunityInfo> getCommunityInfosByCommunityId(Long communityId) {
        LambdaQueryWrapper<CommunityInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommunityInfo::getCommunityId, communityId);
        return list(queryWrapper);
    }

    @Override
    public List<CommunityInfo> getCommunityInfosByDepartmentId(Long departmentId) {
        return communityInfoRepository.findByDepartmentId(departmentId);
    }

    @Override
    public List<CommunityInfo> getVisibleCommunityInfos() {
        return list();
    }

    @Override
    public Page<CommunityInfo> pageVisibleCommunityInfos(int page, int size) {
        return page(new Page<>(page, size));
    }

    @Override
    public String generateQrCodeUrl() {
        return "qr_" + UUID.randomUUID().toString().replace("-", "") + ".png";
    }

    @Override
    public String refreshQrCode(Long id) {
        CommunityInfo communityInfo = getById(id);
        if (communityInfo == null) {
            throw new BusinessException("社区信息不存在");
        }

        // 生成新的二维码URL
        String qrCodeUrl = generateQrCodeUrl();
        communityInfo.setQrcodeUrl(qrCodeUrl);

        // 设置更新信息
        communityInfo.setUpdateTime(LocalDateTime.now());
        communityInfo.setUpdateBy(SecurityUtil.getCurrentUsername());

        updateById(communityInfo);
        return qrCodeUrl;
    }

    @Override
    public String generateQRCode(Long id) {
        return refreshQrCode(id);
    }

    @Override
    public CommunityInfo getCommunityInfoById(Long id) {
        return getById(id);
    }

    @Override
    public Page<CommunityInfo> findVisibleCommunityInfos(Page<CommunityInfo> page, String title, Long departmentId) {
        LambdaQueryWrapper<CommunityInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (title != null && !title.trim().isEmpty()) {
            queryWrapper.like(CommunityInfo::getTitle, title);
        }
        if (departmentId != null) {
            queryWrapper.eq(CommunityInfo::getDepartmentId, departmentId);
        }
        return page(page, queryWrapper);
    }

    @Override
    public Page<CommunityInfo> findCommunityInfos(Page<CommunityInfo> page, String title, Long communityId, Long departmentId) {
        LambdaQueryWrapper<CommunityInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (title != null && !title.trim().isEmpty()) {
            queryWrapper.like(CommunityInfo::getTitle, title);
        }
        if (communityId != null) {
            queryWrapper.eq(CommunityInfo::getCommunityId, communityId);
        }
        if (departmentId != null) {
            queryWrapper.eq(CommunityInfo::getDepartmentId, departmentId);
        }
        return page(page, queryWrapper);
    }
}
