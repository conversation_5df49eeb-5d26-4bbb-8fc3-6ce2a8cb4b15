/*
 Navicat Premium Data Transfer

 Source Server         : 本地mysql8
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : localhost:3308
 Source Schema         : community_management

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 07/06/2025 11:31:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for community
-- ----------------------------
DROP TABLE IF EXISTS `community`;
CREATE TABLE `community`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '社区名称',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社区地址',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社区描述',
  `department_id` bigint NULL DEFAULT NULL COMMENT '所属部门ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `contact_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of community
-- ----------------------------
INSERT INTO `community` VALUES (1, '阳光社区', '北京市朝阳区阳光大道1号', '阳光社区是一个充满活力的社区', 1, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'admin', 'admin', NULL, NULL);
INSERT INTO `community` VALUES (2, '和谐社区', '北京市海淀区和谐路2号', '和谐社区是一个安静祥和的社区', 1, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'admin', 'admin', NULL, NULL);

-- ----------------------------
-- Table structure for community_info
-- ----------------------------
DROP TABLE IF EXISTS `community_info`;
CREATE TABLE `community_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型',
  `community_id` bigint NULL DEFAULT NULL COMMENT '社区ID',
  `department_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `visible` tinyint(1) NULL DEFAULT 1 COMMENT '是否可见',
  `qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '二维码',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK83p2acaou5wdopvopbcrl0e0d`(`community_id` ASC) USING BTREE,
  CONSTRAINT `FK83p2acaou5wdopvopbcrl0e0d` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社区信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of community_info
-- ----------------------------
INSERT INTO `community_info` VALUES (1, '阳光社区活动公告', '本周六上午10点将在社区广场举办义务劳动活动，欢迎居民参加。', '公告', 1, 1, 1, NULL, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'admin', 'admin', NULL, NULL);
INSERT INTO `community_info` VALUES (2, '和谐社区安全提示', '近期天气干燥，请居民注意用火用电安全，防止火灾发生。', '提示', 2, 1, 1, NULL, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'admin', 'admin', NULL, NULL);

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父部门ID',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department` VALUES (1, '总部', NULL, 1, '总部', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system');

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限标识',
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_permission`(`permission` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission
-- ----------------------------
INSERT INTO `permission` VALUES (1, '用户查询', 'user:read', '用户查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (2, '用户创建', 'user:create', '用户创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (3, '用户更新', 'user:update', '用户更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (4, '用户删除', 'user:delete', '用户删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (5, '角色查询', 'role:read', '角色查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (6, '角色创建', 'role:create', '角色创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (7, '角色更新', 'role:update', '角色更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (8, '角色删除', 'role:delete', '角色删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (9, '权限查询', 'permission:read', '权限查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (10, '权限创建', 'permission:create', '权限创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (11, '权限更新', 'permission:update', '权限更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (12, '权限删除', 'permission:delete', '权限删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (13, '部门查询', 'department:read', '部门查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (14, '部门创建', 'department:create', '部门创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (15, '部门更新', 'department:update', '部门更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (16, '部门删除', 'department:delete', '部门删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (17, '社区查询', 'community:read', '社区查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (18, '社区创建', 'community:create', '社区创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (19, '社区更新', 'community:update', '社区更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (20, '社区删除', 'community:delete', '社区删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (21, '社区信息查询', 'community_info:read', '社区信息查询权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (22, '社区信息创建', 'community_info:create', '社区信息创建权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (23, '社区信息更新', 'community_info:update', '社区信息更新权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');
INSERT INTO `permission` VALUES (24, '社区信息删除', 'community_info:delete', '社区信息删除权限', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system', '');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, 'ROLE_ADMIN', '管理员', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system');
INSERT INTO `role` VALUES (2, 'ROLE_USER', '普通用户', '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system');

-- ----------------------------
-- Table structure for role_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`role_id`, `permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色权限关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permission
-- ----------------------------
INSERT INTO `role_permission` VALUES (1, 1);
INSERT INTO `role_permission` VALUES (1, 2);
INSERT INTO `role_permission` VALUES (1, 3);
INSERT INTO `role_permission` VALUES (1, 4);
INSERT INTO `role_permission` VALUES (1, 5);
INSERT INTO `role_permission` VALUES (1, 6);
INSERT INTO `role_permission` VALUES (1, 7);
INSERT INTO `role_permission` VALUES (1, 8);
INSERT INTO `role_permission` VALUES (1, 9);
INSERT INTO `role_permission` VALUES (1, 10);
INSERT INTO `role_permission` VALUES (1, 11);
INSERT INTO `role_permission` VALUES (1, 12);
INSERT INTO `role_permission` VALUES (1, 13);
INSERT INTO `role_permission` VALUES (1, 14);
INSERT INTO `role_permission` VALUES (1, 15);
INSERT INTO `role_permission` VALUES (1, 16);
INSERT INTO `role_permission` VALUES (1, 17);
INSERT INTO `role_permission` VALUES (1, 18);
INSERT INTO `role_permission` VALUES (1, 19);
INSERT INTO `role_permission` VALUES (1, 20);
INSERT INTO `role_permission` VALUES (1, 21);
INSERT INTO `role_permission` VALUES (1, 22);
INSERT INTO `role_permission` VALUES (1, 23);
INSERT INTO `role_permission` VALUES (1, 24);
INSERT INTO `role_permission` VALUES (2, 1);
INSERT INTO `role_permission` VALUES (2, 5);
INSERT INTO `role_permission` VALUES (2, 9);
INSERT INTO `role_permission` VALUES (2, 13);
INSERT INTO `role_permission` VALUES (2, 17);
INSERT INTO `role_permission` VALUES (2, 21);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `department_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2a$10$M6eVCt9/QG96bXKGC2oOR.lZxQptpJEgXd9IsXyW6pHPZzVumv7Ii', '管理员', '<EMAIL>', '13800000000', NULL, 1, 1, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system');
INSERT INTO `user` VALUES (2, 'user', '$2a$10$M6eVCt9/QG96bXKGC2oOR.lZxQptpJEgXd9IsXyW6pHPZzVumv7Ii', '普通用户', '<EMAIL>', '13900000000', NULL, 1, 1, '2025-06-05 02:10:41', '2025-06-05 02:10:41', 'system', 'system');

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO `user_role` VALUES (1, 1);
INSERT INTO `user_role` VALUES (2, 2);

SET FOREIGN_KEY_CHECKS = 1;
