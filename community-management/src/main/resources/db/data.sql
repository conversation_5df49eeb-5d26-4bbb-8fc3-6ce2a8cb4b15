USE community_management;

-- 插入部门数据
INSERT INTO `department` (`id`, `name`, `parent_id`, `sort`, `description`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, '总部', NULL, 1, '总部', NOW(), NOW(), 'system', 'system');

-- 插入权限数据
INSERT INTO `permission` (`id`, `name`, `permission`, `description`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, '用户查询', 'user:read', '用户查询权限', NOW(), NOW(), 'system', 'system'),
(2, '用户创建', 'user:create', '用户创建权限', NOW(), NOW(), 'system', 'system'),
(3, '用户更新', 'user:update', '用户更新权限', NOW(), NOW(), 'system', 'system'),
(4, '用户删除', 'user:delete', '用户删除权限', NOW(), NOW(), 'system', 'system'),
(5, '角色查询', 'role:read', '角色查询权限', NOW(), NOW(), 'system', 'system'),
(6, '角色创建', 'role:create', '角色创建权限', NOW(), NOW(), 'system', 'system'),
(7, '角色更新', 'role:update', '角色更新权限', NOW(), NOW(), 'system', 'system'),
(8, '角色删除', 'role:delete', '角色删除权限', NOW(), NOW(), 'system', 'system'),
(9, '权限查询', 'permission:read', '权限查询权限', NOW(), NOW(), 'system', 'system'),
(10, '权限创建', 'permission:create', '权限创建权限', NOW(), NOW(), 'system', 'system'),
(11, '权限更新', 'permission:update', '权限更新权限', NOW(), NOW(), 'system', 'system'),
(12, '权限删除', 'permission:delete', '权限删除权限', NOW(), NOW(), 'system', 'system'),
(13, '部门查询', 'department:read', '部门查询权限', NOW(), NOW(), 'system', 'system'),
(14, '部门创建', 'department:create', '部门创建权限', NOW(), NOW(), 'system', 'system'),
(15, '部门更新', 'department:update', '部门更新权限', NOW(), NOW(), 'system', 'system'),
(16, '部门删除', 'department:delete', '部门删除权限', NOW(), NOW(), 'system', 'system'),
(17, '社区查询', 'community:read', '社区查询权限', NOW(), NOW(), 'system', 'system'),
(18, '社区创建', 'community:create', '社区创建权限', NOW(), NOW(), 'system', 'system'),
(19, '社区更新', 'community:update', '社区更新权限', NOW(), NOW(), 'system', 'system'),
(20, '社区删除', 'community:delete', '社区删除权限', NOW(), NOW(), 'system', 'system'),
(21, '社区信息查询', 'community_info:read', '社区信息查询权限', NOW(), NOW(), 'system', 'system'),
(22, '社区信息创建', 'community_info:create', '社区信息创建权限', NOW(), NOW(), 'system', 'system'),
(23, '社区信息更新', 'community_info:update', '社区信息更新权限', NOW(), NOW(), 'system', 'system'),
(24, '社区信息删除', 'community_info:delete', '社区信息删除权限', NOW(), NOW(), 'system', 'system');

-- 插入角色数据
INSERT INTO `role` (`id`, `name`, `description`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, 'ROLE_ADMIN', '管理员', NOW(), NOW(), 'system', 'system'),
(2, 'ROLE_USER', '普通用户', NOW(), NOW(), 'system', 'system');

-- 插入角色权限关联数据
-- 管理员拥有所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10),
(1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17), (1, 18), (1, 19), (1, 20),
(1, 21), (1, 22), (1, 23), (1, 24);

-- 普通用户只有查询权限
INSERT INTO `role_permission` (`role_id`, `permission_id`) VALUES
(2, 1), (2, 5), (2, 9), (2, 13), (2, 17), (2, 21);

-- 插入用户数据（密码使用BCrypt加密，这里的密码是明文的"admin"和"user"）
INSERT INTO `user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `enabled`, `department_id`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, 'admin', '$2a$10$rDkPvvAFV8kqwvKJzwuRa.MDL1l/u1wLO.Zz.whSxBwCYYtCj/aUe', '管理员', '<EMAIL>', '13800000000', 1, 1, NOW(), NOW(), 'system', 'system'),
(2, 'user', '$2a$10$RpFJjxYiCdVGCOqGj3Tjqu5jPMjGgHPrGIbT.yt6zQlOIWyZrXjDm', '普通用户', '<EMAIL>', '13900000000', 1, 1, NOW(), NOW(), 'system', 'system');

-- 插入用户角色关联数据
INSERT INTO `user_role` (`user_id`, `role_id`) VALUES
(1, 1),
(2, 2);

-- 插入示例社区数据
INSERT INTO `community` (`id`, `name`, `address`, `description`, `department_id`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, '阳光社区', '北京市朝阳区阳光大道1号', '阳光社区是一个充满活力的社区', 1, NOW(), NOW(), 'admin', 'admin'),
(2, '和谐社区', '北京市海淀区和谐路2号', '和谐社区是一个安静祥和的社区', 1, NOW(), NOW(), 'admin', 'admin');

-- 插入示例社区信息数据
INSERT INTO `community_info` (`id`, `title`, `content`, `type`, `community_id`, `department_id`, `visible`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(1, '阳光社区活动公告', '本周六上午10点将在社区广场举办义务劳动活动，欢迎居民参加。', '公告', 1, 1, 1, NOW(), NOW(), 'admin', 'admin'),
(2, '和谐社区安全提示', '近期天气干燥，请居民注意用火用电安全，防止火灾发生。', '提示', 2, 1, 1, NOW(), NOW(), 'admin', 'admin');