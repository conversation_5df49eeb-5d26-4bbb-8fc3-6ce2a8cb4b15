server:
  port: 9090
  servlet:
    context-path: /api

spring:
  application:
    name: community-management

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************
    username: root
    password: 111111

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.community.management.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: community-management-secret-key
  expiration: 86400000  # 24小时
  tokenHeader: Authorization
  tokenPrefix: Bearer

# 文件上传配置
file:
  upload-dir: ./uploads/
  qrcode-dir: ./qrcodes/
