{"name": "community-ui-v2", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^1.9.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "sass": "^1.89.1", "sass-loader": "^10.5.2", "vue": "^2.6.14", "vue-router": "^3.6.5", "vue-template-compiler": "^2.6.14", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}