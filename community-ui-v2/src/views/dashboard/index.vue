<template>
  <div class="dashboard-container">
    <div class="dashboard-text">欢迎使用社区管理系统</div>
    
    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="peoples" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户数量</div>
            <div class="card-panel-num">{{ userCount }}</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">社区数量</div>
            <div class="card-panel-num">{{ communityCount }}</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="money" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">部门数量</div>
            <div class="card-panel-num">{{ departmentCount }}</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="shopping" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">角色数量</div>
            <div class="card-panel-num">{{ roleCount }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>系统信息</span>
          </div>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统名称：</span>
              <span>社区管理系统</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span>v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器时间：</span>
              <span>{{ currentTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行环境：</span>
              <span>Spring Boot + Vue.js</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>快捷操作</span>
          </div>
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-user" @click="$router.push('/system/user')">用户管理</el-button>
            <el-button type="success" icon="el-icon-s-custom" @click="$router.push('/system/role')">角色管理</el-button>
            <el-button type="info" icon="el-icon-office-building" @click="$router.push('/system/department')">部门管理</el-button>
            <el-button type="warning" icon="el-icon-location" @click="$router.push('/community/list')">社区管理</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  data() {
    return {
      userCount: 0,
      communityCount: 0,
      departmentCount: 0,
      roleCount: 0,
      currentTime: ''
    }
  },
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  created() {
    this.getStatistics()
    this.updateTime()
    setInterval(this.updateTime, 1000)
  },
  methods: {
    getStatistics() {
      // 这里可以调用API获取统计数据
      // 暂时使用模拟数据
      this.userCount = 156
      this.communityCount = 23
      this.departmentCount = 8
      this.roleCount = 5
    },
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
  }
}

.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
        background: #40c9c6;
      }
      .icon-message {
        background: #36a3f7;
      }
      .icon-money {
        background: #f4516c;
      }
      .icon-shopping {
        background: #34bfa3
      }
    }

    .icon-people {
      color: #40c9c6;
    }
    .icon-message {
      color: #36a3f7;
    }
    .icon-money {
      color: #f4516c;
    }
    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

.system-info {
  .info-item {
    margin-bottom: 12px;
    line-height: 20px;
    
    .info-label {
      font-weight: bold;
      color: #666;
    }
  }
}

.quick-actions {
  .el-button {
    margin-bottom: 10px;
    margin-right: 10px;
  }
}
</style>