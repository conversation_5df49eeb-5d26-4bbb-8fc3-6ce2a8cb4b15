<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div class="text-center">
              <el-avatar :size="100" :src="user.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"></el-avatar>
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <div class="item-label">用户名</div>
                <div class="item-content">{{ user.username }}</div>
              </li>
              <li class="list-group-item">
                <div class="item-label">姓名</div>
                <div class="item-content">{{ user.name }}</div>
              </li>
              <li class="list-group-item">
                <div class="item-label">部门</div>
                <div class="item-content">{{ user.departmentName }}</div>
              </li>
              <li class="list-group-item">
                <div class="item-label">角色</div>
                <div class="item-content">{{ roleNames }}</div>
              </li>
              <li class="list-group-item">
                <div class="item-label">创建时间</div>
                <div class="item-content">{{ user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <el-form ref="form" :model="user" :rules="rules" label-width="80px">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="user.username" disabled />
                </el-form-item>
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="user.name" />
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                  <el-input v-model="user.phone" maxlength="11" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="user.email" maxlength="50" />
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="user.gender">
                    <el-radio label="0">男</el-radio>
                    <el-radio label="1">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submit">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <el-form ref="pwdForm" :model="pwdForm" :rules="pwdRules" label-width="100px">
                <el-form-item label="旧密码" prop="oldPassword">
                  <el-input v-model="pwdForm.oldPassword" placeholder="请输入旧密码" type="password" show-password />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="pwdForm.newPassword" placeholder="请输入新密码" type="password" show-password />
                </el-form-item>
                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input v-model="pwdForm.confirmPassword" placeholder="请确认新密码" type="password" show-password />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitPwd">保存</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getUser, updateUser } from '@/api/user'
import { validEmail } from '@/utils/validate'

export default {
  name: 'Profile',
  data() {
    const validateEmail = (rule, value, callback) => {
      if (!value || value === '') {
        callback()
      } else if (!validEmail(value)) {
        callback(new Error('请输入正确的邮箱格式'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      if (!value || value === '') {
        callback()
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.pwdForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    return {
      user: {
        id: undefined,
        username: '',
        name: '',
        email: '',
        phone: '',
        gender: '0',
        avatar: '',
        departmentName: '',
        createTime: ''
      },
      pwdForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      activeTab: 'userinfo',
      roles: [],
      rules: {
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ],
        phone: [
          { validator: validatePhone, trigger: 'blur' }
        ]
      },
      pwdRules: {
        oldPassword: [
          { required: true, message: '旧密码不能为空', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    roleNames() {
      return this.roles.map(role => role.name).join(', ')
    }
  },
  created() {
    this.getUser()
  },
  methods: {
    getUser() {
      const userId = this.$store.getters.userId
      getUser(userId).then(response => {
        this.user = response.data
        this.roles = response.data.roles || []
      })
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          updateUser(this.user).then(response => {
            this.$message.success('个人信息修改成功')
          })
        }
      })
    },
    submitPwd() {
      this.$refs['pwdForm'].validate(valid => {
        if (valid) {
          // 调用修改密码API
          this.$message.success('密码修改成功')
          this.pwdForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

.list-group-striped {
  padding: 0;
  margin: 0;
  list-style: none;

  .list-group-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
    display: flex;

    &:last-child {
      border-bottom: none;
    }

    .item-label {
      color: #999;
      width: 80px;
    }

    .item-content {
      flex: 1;
      color: #333;
    }
  }
}
</style>