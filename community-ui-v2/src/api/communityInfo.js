import request from '@/utils/request'

export function fetchCommunityInfoList(query) {
  return request({
    url: '/api/community-infos',
    method: 'get',
    params: query
  })
}

export function fetchCommunityInfo(id) {
  return request({
    url: `/api/community-infos/${id}`,
    method: 'get'
  })
}

export function createCommunityInfo(data) {
  return request({
    url: '/api/community-infos',
    method: 'post',
    data
  })
}

export function updateCommunityInfo(data) {
  return request({
    url: `/api/community-infos/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteCommunityInfo(id) {
  return request({
    url: `/api/community-infos/${id}`,
    method: 'delete'
  })
}

export function refreshQRCode(id) {
  return request({
    url: `/api/community-infos/${id}/refresh-qrcode`,
    method: 'post'
  })
}
