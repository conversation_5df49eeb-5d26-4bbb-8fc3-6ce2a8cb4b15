import request from '@/utils/request'

// 查询社区列表
export function listCommunity(query) {
  return request({
    url: '/community/list',
    method: 'get',
    params: query
  })
}

// 查询社区详细
export function getCommunity(communityId) {
  return request({
    url: '/community/' + communityId,
    method: 'get'
  })
}

// 新增社区
export function addCommunity(data) {
  return request({
    url: '/community',
    method: 'post',
    data: data
  })
}

// 修改社区
export function updateCommunity(data) {
  return request({
    url: '/community',
    method: 'put',
    data: data
  })
}

// 删除社区
export function delCommunity(communityId) {
  return request({
    url: '/community/' + communityId,
    method: 'delete'
  })
}

// 查询社区信息列表
export function listCommunityInfo(query) {
  return request({
    url: '/community/info/list',
    method: 'get',
    params: query
  })
}

// 查询社区信息详细
export function getCommunityInfo(infoId) {
  return request({
    url: '/community/info/' + infoId,
    method: 'get'
  })
}

// 新增社区信息
export function addCommunityInfo(data) {
  return request({
    url: '/community/info',
    method: 'post',
    data: data
  })
}

// 修改社区信息
export function updateCommunityInfo(data) {
  return request({
    url: '/community/info',
    method: 'put',
    data: data
  })
}

// 删除社区信息
export function delCommunityInfo(infoId) {
  return request({
    url: '/community/info/' + infoId,
    method: 'delete'
  })
}