import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/profile'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const asyncRoutes = [
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    name: 'System',
    meta: { title: '系统管理', icon: 'setting', roles: ['ROLE_ADMIN'] },
    children: [
      {
        path: 'user',
        component: () => import('@/views/system/user'),
        name: 'User',
        meta: { title: '用户管理', icon: 'user', roles: ['ROLE_ADMIN'] }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role'),
        name: 'Role',
        meta: { title: '角色管理', icon: 'peoples', roles: ['ROLE_ADMIN'] }
      },
      {
        path: 'permission',
        component: () => import('@/views/system/permission'),
        name: 'Permission',
        meta: { title: '权限管理', icon: 'tree', roles: ['ROLE_ADMIN'] }
      },
      {
        path: 'department',
        component: () => import('@/views/system/department'),
        name: 'Department',
        meta: { title: '部门管理', icon: 'tree-table', roles: ['ROLE_ADMIN'] }
      }
    ]
  },
  {
    path: '/community',
    component: Layout,
    redirect: '/community/list',
    name: 'Community',
    meta: { title: '社区管理', icon: 'international' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/community/list'),
        name: 'CommunityList',
        meta: { title: '社区列表', icon: 'list' }
      },
      {
        path: 'info',
        component: () => import('@/views/community/info'),
        name: 'CommunityInfo',
        meta: { title: '社区信息', icon: 'documentation' }
      }
    ]
  },
  // 404 页面必须放在末尾
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// 重置路由
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router