import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      // 让每个请求携带token
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }
    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 如果返回的状态码不是200，说明接口请求失败
    // 错误信息由各个页面自己处理，拦截器只处理认证相关的错误
    if (res.code !== 200) {
      // 401: 未登录或token过期
      // 403: 权限不足
      if (res.code === 401 || res.code === 403) {
        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error)

    // HTTP错误也让页面自己处理，拦截器只负责传递错误信息
    let message = '请求失败'
    if (error.response && error.response.data) {
      const errorData = error.response.data
      if (errorData.message) {
        message = errorData.message
      } else if (typeof errorData === 'string') {
        message = errorData
      }
    } else if (error.message) {
      message = error.message
    }

    // 创建一个包含详细错误信息的错误对象
    const customError = new Error(message)
    customError.response = error.response
    customError.originalError = error

    return Promise.reject(customError)
  }
)

export default service