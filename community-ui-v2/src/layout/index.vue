<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
      </div>
      <app-main />
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain } from './components'
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.getters.sidebar
    },
    device() {
      return this.$store.getters.device
    },
    fixedHeader() {
      return false
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
@use "~@/styles/mixin.scss" as mixins;
@use "~@/styles/variables.scss" as vars;

.app-wrapper {
  @include mixins.clearfix;
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  background-color: #f5f5f5;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }

  .sidebar-container {
    flex-shrink: 0;
    width: vars.$sideBarWidth !important;
    height: 100vh;
    position: relative;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    box-shadow: 2px 0 6px rgba(0,21,41,.35);
  }

  .main-container {
    flex: 1;
    min-height: 100vh;
    transition: margin-left .28s;
    margin-left: 0;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  &.hideSidebar .main-container {
    margin-left: 0;
  }
}
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{vars.$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px)
}

.mobile .fixed-header {
  width: 100%;
}
</style>