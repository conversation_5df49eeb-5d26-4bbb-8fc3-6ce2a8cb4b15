const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false, // 禁用ESLint检查

  devServer: {
    port: 8080,
    open: false,
    client: {
      overlay: {
        warnings: false,
        errors: true
      }
    }
  },

  // 配置sass-loader
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          // 禁用legacy API警告
          silenceDeprecations: ['legacy-js-api']
        }
      },
      scss: {
        sassOptions: {
          // 禁用legacy API警告
          silenceDeprecations: ['legacy-js-api']
        }
      }
    }
  },

  // 生产环境配置
  productionSourceMap: false
})
