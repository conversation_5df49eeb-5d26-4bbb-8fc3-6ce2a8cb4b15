import request from '@/utils/request'

// 查询部门列表
export function listDepartment(query) {
  return request({
    url: '/department/list',
    method: 'get',
    params: query
  })
}

// 查询部门详细
export function getDepartment(departmentId) {
  return request({
    url: '/department/' + departmentId,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeDepartment() {
  return request({
    url: '/department/tree',
    method: 'get'
  })
}

// 新增部门
export function addDepartment(data) {
  return request({
    url: '/department',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDepartment(data) {
  return request({
    url: '/department',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDepartment(departmentId) {
  return request({
    url: '/department/' + departmentId,
    method: 'delete'
  })
}