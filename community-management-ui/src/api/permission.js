import request from '@/utils/request'

// 查询权限列表
export function listPermission(query) {
  return request({
    url: '/permission/list',
    method: 'get',
    params: query
  })
}

// 查询权限详细
export function getPermission(permissionId) {
  return request({
    url: '/permission/' + permissionId,
    method: 'get'
  })
}

// 新增权限
export function addPermission(data) {
  return request({
    url: '/permission',
    method: 'post',
    data: data
  })
}

// 修改权限
export function updatePermission(data) {
  return request({
    url: '/permission',
    method: 'put',
    data: data
  })
}

// 删除权限
export function delPermission(permissionId) {
  return request({
    url: '/permission/' + permissionId,
    method: 'delete'
  })
}

// 查询权限树
export function treePermission() {
  return request({
    url: '/permission/tree',
    method: 'get'
  })
}

// 根据角色ID查询权限
export function getRolePermissions(roleId) {
  return request({
    url: '/permission/role/' + roleId,
    method: 'get'
  })
}