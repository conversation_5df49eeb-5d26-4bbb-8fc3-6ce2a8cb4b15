import './waves.css'

const context = '@@wavesContext'

function isRippleEnabled(value) {
  return value !== false
}

function rippleShow(e) {
  const container = e.currentTarget
  const instance = container[context]

  // Create ripple
  const ripple = document.createElement('div')
  ripple.className = 'waves-ripple'

  // Get click coordinates
  const rect = container.getBoundingClientRect()
  const left = rect.left
  const top = rect.top
  const width = container.offsetWidth
  const height = container.offsetHeight
  const dx = e.clientX - left
  const dy = e.clientY - top
  const maxX = Math.max(dx, width - dx)
  const maxY = Math.max(dy, height - dy)
  const style = window.getComputedStyle(container)
  const radius = Math.sqrt(maxX * maxX + maxY * maxY)

  // Apply styles
  ripple.style.width = ripple.style.height = Math.max(height, width) * 2 + 'px'
  ripple.style.left = dx - radius + 'px'
  ripple.style.top = dy - radius + 'px'

  // Ripple color
  const color = instance.color || style.color || style.borderColor || style.backgroundColor || '#fff'
  ripple.style.backgroundColor = color

  // Add ripple to DOM
  container.appendChild(ripple)

  // Play animation
  setTimeout(() => {
    ripple.classList.add('waves-ripple-animate')
  }, 0)

  function rippleHide() {
    ripple.classList.add('waves-ripple-animate-out')
    setTimeout(() => {
      let ripples = container.querySelectorAll('.waves-ripple')
      if (ripples.length === 0) return
      container.removeChild(ripple)
    }, 300)
    setTimeout(() => {
      if (instance.mousedown === false) {
        container.classList.remove('waves-active')
      }
    }, 400)
  }

  if (instance.mousedown === true) {
    instance.mousedown = false
    rippleHide()
  } else {
    setTimeout(() => {
      rippleHide()
    }, 400)
  }
}

function rippleOut(e) {
  const container = e.currentTarget
  const instance = container[context]
  if (instance) {
    instance.mousedown = false
  }
}

function rippleIn(e) {
  const container = e.currentTarget
  const instance = container[context]
  if (instance) {
    instance.mousedown = true
  }
}

function updateRipple(el, binding, wasEnabled) {
  const enabled = isRippleEnabled(binding.value)
  if (!enabled) {
    rippleOut({ currentTarget: el })
  }
  el[context] = el[context] || {}
  el[context].enabled = enabled
  el[context].color = binding.arg

  if (enabled && !wasEnabled) {
    el.classList.add('waves-effect')
    el.addEventListener('mousedown', rippleIn, { passive: true })
    el.addEventListener('mouseup', rippleOut, { passive: true })
    el.addEventListener('mouseleave', rippleOut, { passive: true })
    // Anchor tags can be dragged, causes other hides to fail - #1537
    el.addEventListener('dragstart', rippleOut, { passive: true })
    el.addEventListener('click', rippleShow, { passive: true })
  } else if (!enabled && wasEnabled) {
    removeListeners(el)
  }
}

function removeListeners(el) {
  el.removeEventListener('mousedown', rippleIn)
  el.removeEventListener('mouseup', rippleOut)
  el.removeEventListener('mouseleave', rippleOut)
  el.removeEventListener('dragstart', rippleOut)
  el.removeEventListener('click', rippleShow)
}

function directive(el, binding) {
  updateRipple(el, binding, false)
}

function unbind(el) {
  delete el[context]
  removeListeners(el)
}

function update(el, binding) {
  if (binding.value === binding.oldValue) {
    return
  }
  const wasEnabled = isRippleEnabled(binding.oldValue)
  updateRipple(el, binding, wasEnabled)
}

export default {
  bind: directive,
  unbind,
  update
}
