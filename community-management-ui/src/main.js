import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './assets/styles/index.scss'
import './permission' // 权限控制
import { parseTime } from '@/utils'

Vue.use(ElementUI, { size: 'medium' })

// 注册全局过滤器
Vue.filter('parseTime', parseTime)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')