{"name": "community-management-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "node --max-old-space-size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build": "node --max-old-space-size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "core-js": "^3.6.5", "element-ui": "^2.15.6", "js-cookie": "^3.0.1", "nprogress": "^0.2.0", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.27.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}